var n = {};

function s() {
    var t;
    if (window.location.href.includes("/lp") || window.location.href.includes("/funnel")) {
        let e = $("#checkout-data"),
            i = JSON.parse(e.text());
        n = {
            item_name: i.funnelSku,
            price: (t = i.price) == null ? void 0 : t.price,
            quantity: 1,
            currency: i.nativeCurrency
        }
    }
}

function o(t, e) {
    typeof window.dataLayer > "u" || window.dataLayer.push({
        event: "purchase",
        ecommerce: {
            value: t,
            currency: e
        }
    })
}

function d(t, e = {}) {
    typeof window.dataLayer > "u" || window.dataLayer.push({
        event: "add_to_cart",
        ecommerce: {
            items: [{
                item_id: t,
                item_name: e.item_name ? ? "",
                price: e.price ? ? 0,
                quantity: e.quantity ? ? 1,
                currency: e.currency ? ? ""
            }]
        }
    })
}

function u() {
    const t = "ATC",
        e = r("AddToCart");
    a(t), d(e, n), typeof fbq < "u" && fbq("track", "AddToCart", {
        item_name: n.item_name,
        price: n.price ? ? 0
    }, {
        eventID: e
    }), typeof ttq < "u" && ttq.track("AddToCart", {
        content_type: "product",
        content_id: e,
        value: n.price ? ? 0,
        currency: n.currency ? ? "",
        number_items: n.quantity ? ? 1
    }), typeof snaptr < "u" && snaptr("track", "ADD_CART", {
        price: n.price ? ? 0,
        currency: n.currency ? ? "",
        number_items: n.quantity ? ? 1,
        uuid_c1: e
    }), typeof pintrk < "u" && pintrk("track", "addtocart")
}

function w() {
    if (window.location.href.includes("type=success")) return;
    const t = "PageView",
        e = r(t);
    a(t), typeof fbq < "u" && fbq("track", "PageView", {}, {
        eventID: e
    }), typeof ttq < "u" && ttq.track("PageView"), typeof snaptr < "u" && snaptr("track", "PAGE_VIEW"), typeof pintrk < "u" && pintrk("track", "pagevisit")
}

function y() {
    if (window.location.href.includes("type=success")) return;
    const t = "VC",
        e = r("ViewContent");
    a(t), typeof fbq < "u" && fbq("track", "ViewContent", {}, {
        eventID: e
    }), typeof ttq < "u" && ttq.track("ViewContent", {
        content_type: "product",
        content_id: e
    }), typeof snaptr < "u" && snaptr("track", "VIEW_CONTENT"), typeof pintrk < "u" && pintrk("track", "pagevisit")
}

function l(t, e) {
    const i = "Purchase",
        c = r(i);
    o(t, e), typeof fbq < "u" && fbq("track", i, {
        currency: e,
        value: t
    }, {
        eventID: c
    }), typeof ttq < "u" && ttq.track("PlaceAnOrder", {
        currency: e,
        value: t,
        content_type: "product",
        content_id: 0
    }), typeof snaptr < "u" && snaptr("track", "PURCHASE", {
        currency: e,
        price: t,
        client_dedup_id: c
    }), typeof pintrk < "u" && pintrk("track", "checkout", {
        value: t,
        currency: e
    })
}

function a(t) {
    var e = $(".home-page").attr("data-funnel") ? ? 0;
    $.ajax({
        url: "/api/logEvent",
        data: {
            event: t,
            funnel_id: e
        },
        dataType: "json",
        type: "post",
        success: function(i) {}
    })
}

function v() {
    $.ajaxSetup({
        headers: {
            "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content")
        }
    }), $.ajax({
        url: "/api/upsell/update",
        type: "POST",
        data: {
            slug: upsellSlug
        },
        success: function(t) {},
        error: function(t) {}
    })
}

function r(t) {
    const e = Math.floor(Date.now() / 1e3),
        i = Math.floor(e / 60),
        f = t.replace(/[^a-zA-Z0-9]/g, "").substring(0, 10),
        p = i.toString(36);
    return f + "_" + p
}
$(document).ready(function() {
    s(), $(".AddToCart").click(function() {
        u()
    }), window.eventViewContent()
});
window.eventAddToCart = u;
window.trackPurchaseForGTM = o;
window.trackAddToCartForGTM = d;
window.upsellViewedEvent = v;
window.eventViewContent = y;
window.eventPurchase = l;
window.eventPageView = w;
window.eventPageView();