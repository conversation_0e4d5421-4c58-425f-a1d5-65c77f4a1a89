/*!
 * Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com
 * License - https://fontawesome.com/license (Commercial License)
 * Copyright 2024 Fonticons, Inc.
 */

@font-face {
    font-family: "Font Awesome 5 Brands";
    font-display: block;
    font-weight: 400;
    src: url(../webfonts/pro-fa-brands-400-0.woff2) format("woff2"), url(../webfonts/pro-fa-brands-400-0.ttf) format("truetype");
    unicode-range: u+e007, u+e013, u+e01a, u+e01e, u+e049, u+e052, u+e055-e057, u+e077-e084, u+e087-e088, u+f081-f082, u+f08c, u+f092, u+f099-f09b, u+f0d2-f0d5, u+f0e1, u+f113, u+f136, u+f13b-f13c, u+f15a, u+f167-f169, u+f16b-f16e, u+f170-f171, u+f173-f174, u+f179-f17e, u+f180-f181, u+f184, u+f189-f18d, u+f194, u+f198, u+f19a-f19b, u+f19e, u+f1a0-f1a4, u+f1b4, u+f1bc, u+f1be, u+f1e8, u+f1ed, u+f1f0-f1f1, u+f20e, u+f210, u+f213-f214, u+f232, u+f23a, u+f26b, u+f270, u+f288, u+f299, u+f2a6, u+f2b0, u+f2c5-f2c6, u+f2e0, u+f368, u+f379, u+f392-f393, u+f39f, u+f3a9, u+f3ab-f3ac, u+f3c0, u+f3c7, u+f3ca, u+f3e2, u+f3eb-f3ec, u+f3ef, u+f3f8, u+f3fe, u+f419, u+f41b, u+f4d5, u+f4e4, u+f4f8-f4f9, u+f514, u+f5b5, u+f6c9, u+f731, u+f77b, u+f7af, u+f7e1, u+f83b
}

@font-face {
    font-family: "Font Awesome 5 Brands";
    font-display: block;
    font-weight: 400;
    src: url(../webfonts/pro-fa-brands-400-1.woff2) format("woff2"), url(../webfonts/pro-fa-brands-400-1.ttf) format("truetype");
    unicode-range: u+f1a5-f1aa, u+f1b5-f1b7, u+f1bd, u+f1ca-f1cc, u+f1d0-f1d7, u+f1e7, u+f1e9, u+f1ee, u+f1f2-f1f5, u+f202-f203, u+f208-f209, u+f20d, u+f211-f212, u+f215-f216, u+f231, u+f237, u+f23b-f23e, u+f24b-f24c, u+f25e, u+f260-f261, u+f263-f26a, u+f26d-f26e, u+f27c-f27e, u+f280-f282, u+f284-f287, u+f289-f28a, u+f293-f294, u+f296-f298, u+f2a5, u+f2a9-f2ae, u+f2b1-f2b4, u+f2b8, u+f2c4, u+f2d5-f2da, u+f2dd-f2de, u+f35c, u+f369-f375, u+f378, u+f37a-f37d, u+f37f-f380, u+f383-f385, u+f388, u+f38b-f38f, u+f391, u+f394-f397, u+f399-f39a, u+f39d-f39e, u+f3a1-f3a4, u+f3a6-f3a8, u+f3aa, u+f3ad-f3b2, u+f3b4-f3bd, u+f3c3-f3c4, u+f3c6, u+f3c8, u+f3cb-f3cc, u+f3d0, u+f3d2-f3dc, u+f3df, u+f425, u+f4e6
}

@font-face {
    font-family: "Font Awesome 5 Brands";
    font-display: block;
    font-weight: 400;
    src: url(../webfonts/pro-fa-brands-400-2.woff2) format("woff2"), url(../webfonts/pro-fa-brands-400-2.ttf) format("truetype");
    unicode-range: u+f3e1, u+f3e3-f3e4, u+f3e6-f3ea, u+f3ee, u+f3f3, u+f3f5-f3f7, u+f3f9, u+f402-f405, u+f407-f40d, u+f411-f417, u+f41a, u+f41c-f421, u+f423, u+f426-f431, u+f44d, u+f452, u+f457, u+f459, u+f4e5, u+f4e7-f4f7, u+f50a-f513, u+f592, u+f59e, u+f5a3, u+f5a8, u+f5b2, u+f5be, u+f5c6, u+f5cc, u+f5cf, u+f5f1, u+f5f7, u+f5fa, u+f60f, u+f612, u+f63f, u+f642, u+f69d, u+f6ca, u+f6cc, u+f6dc, u+f730, u+f75d, u+f77a, u+f785, u+f789, u+f78d, u+f790-f791, u+f797-f799, u+f7b0-f7b1, u+f7b3, u+f7bb-f7bc, u+f7c6, u+f7d3, u+f7d6, u+f7df-f7e0, u+f7e3, u+f834-f83a, u+f83c-f83d, u+f83f-f842, u+f89e, u+f8a6, u+f8ca, u+f8d2, u+f8e1, u+f8e8
}

@font-face {
    font-family: "Font Awesome 5 Brands";
    font-display: block;
    font-weight: 400;
    src: url(../webfonts/pro-fa-brands-400.woff2) format("woff2"), url(../webfonts/pro-fa-brands-400.ttf) format("truetype");
    unicode-range: u+a
}

@font-face {
    font-family: "Font Awesome 5 Pro";
    font-display: block;
    font-weight: 900;
    src: url(../webfonts/pro-fa-solid-900-0.woff2) format("woff2"), url(../webfonts/pro-fa-solid-900-0.ttf) format("truetype");
    unicode-range: u+e010, u+e017, u+e01b, u+e01f-e021, u+e024, u+e02f, u+e03a, u+e042, u+e045-e046, u+e060, u+e068, u+e06e, u+e074, u+e076, u+f001, u+f004-f005, u+f007-f008, u+f00c, u+f011-f012, u+f015, u+f017-f019, u+f01c, u+f023-f025, u+f02a, u+f02c-f031, u+f03a, u+f03d-f03e, u+f041, u+f04a-f04e, u+f05b, u+f060-f065, u+f067-f068, u+f06b-f06e, u+f072, u+f075, u+f077-f078, u+f07b, u+f084, u+f086, u+f091, u+f093, u+f095, u+f09c-f09d, u+f0a3, u+f0a6, u+f0ac-f0ad, u+f0b0-f0b1, u+f0c0-f0c2, u+f0c5-f0c6, u+f0c8, u+f128, u+f12a, u+f155, u+f292, u+f295, u+f332, u+f541, u+f80a, u+f80c
}

@font-face {
    font-family: "Font Awesome 5 Pro";
    font-display: block;
    font-weight: 900;
    src: url(../webfonts/pro-fa-solid-900-1.woff2) format("woff2"), url(../webfonts/pro-fa-solid-900-1.ttf) format("truetype");
    unicode-range: u+f040, u+f0c9, u+f0cc, u+f0ce, u+f0d1, u+f0d6-f0d7, u+f0dc, u+f0e0, u+f0e7-f0e8, u+f0eb, u+f0f3, u+f0f8, u+f106, u+f108-f109, u+f10e, u+f110-f111, u+f11c, u+f11e, u+f121, u+f126, u+f129, u+f12c-f12e, u+f130-f133, u+f135, u+f13d, u+f140, u+f145, u+f14e, u+f15b, u+f164, u+f186, u+f188, u+f1ab, u+f1ad-f1ae, u+f1b2, u+f1b8, u+f1bb, u+f1c0-f1c3, u+f1ce, u+f1d8, u+f1dc, u+f1e4-f1e6, u+f1ea-f1ec, u+f1f8-f1f9, u+f205, u+f20a, u+f217, u+f219-f21d, u+f22d, u+f233-f234, u+f238, u+f246, u+f24d, u+f251, u+f25d, u+f275, u+f29e, u+f2a0, u+f2a7, u+f2b5, u+f2bb, u+f2cc-f2cd, u+f2d2, u+f2db, u+f2e1, u+f2ec, u+f2f7, u+f2fc, u+f302-f303, u+f316, u+f31a, u+f328, u+f335, u+f363, u+f37e, u+f390, u+f3c5, u+f3ce, u+f3e5, u+f3f4, u+f3fb, u+f40e, u+f435, u+f47d
}

@font-face {
    font-family: "Font Awesome 5 Pro";
    font-display: block;
    font-weight: 900;
    src: url(../webfonts/pro-fa-solid-900-2.woff2) format("woff2"), url(../webfonts/pro-fa-solid-900-2.ttf) format("truetype");
    unicode-range: u+f44b, u+f481, u+f48a, u+f48f-f490, u+f493-f494, u+f498, u+f4b7, u+f4ca, u+f4cc, u+f4d1, u+f4d7-f4d8, u+f4e2, u+f503, u+f508, u+f51b, u+f51d-f51e, u+f521-f522, u+f52b, u+f530, u+f535, u+f53e, u+f543-f545, u+f548-f549, u+f54e, u+f555, u+f559, u+f55d, u+f564, u+f56c, u+f56e-f570, u+f577-f578, u+f590, u+f594-f595, u+f5a1-f5a2, u+f5aa-f5ab, u+f5b0, u+f5b7, u+f5ba, u+f5bf, u+f5ca, u+f5db-f5dc, u+f5ef, u+f5f2, u+f5f6, u+f5fb, u+f5fd, u+f621, u+f63b, u+f649-f64a, u+f64f, u+f653-f654, u+f656, u+f65b, u+f664, u+f673, u+f675, u+f67d, u+f67f, u+f695, u+f69c, u+f6a8, u+f6bf-f6c0, u+f6d5, u+f6e3, u+f6e9, u+f6f5, u+f6fa, u+f6ff-f700, u+f70b, u+f70e, u+f715, u+f71b, u+f72e-f72f, u+f733-f734, u+f747, u+f755, u+f757, u+f75c, u+f762, u+f773, u+f77c, u+f781, u+f784, u+f788, u+f7b2, u+f7b6, u+f7bd, u+f7d5, u+f7ee, u+f7ff, u+f801, u+f804, u+f813-f814, u+f82f-f830, u+f845-f846, u+f850, u+f855, u+f858-f859, u+f85c, u+f866, u+f86d, u+f871, u+f875, u+f893-f894, u+f897, u+f89f, u+f8a9, u+f8b1-f8b2, u+f8bb, u+f8c7, u+f8d6-f8d7, u+f8d9
}

@font-face {
    font-family: "Font Awesome 5 Pro";
    font-display: block;
    font-weight: 900;
    src: url(../webfonts/pro-fa-solid-900-3.woff2) format("woff2"), url(../webfonts/pro-fa-solid-900-3.ttf) format("truetype");
    unicode-range: u+e000-e006, u+e008-e00f, u+e011-e012, u+e014-e016, u+e018-e019, u+e01c-e01d, u+e022-e023, u+e025-e02e, u+e030-e039, u+e03b-e041, u+e043-e044, u+e047-e048, u+e04a-e051, u+e053-e054, u+e058-e05f, u+f069, u+f1fa, u+f52c, u+f531, u+f536, u+f69f, u+f8df-f8e0, u+f8e7, u+f8ee-f8ef, u+f8fd
}

@font-face {
    font-family: "Font Awesome 5 Pro";
    font-display: block;
    font-weight: 900;
    src: url(../webfonts/pro-fa-solid-900-4.woff2) format("woff2"), url(../webfonts/pro-fa-solid-900-4.ttf) format("truetype");
    unicode-range: u+e061-e067, u+e069-e06d, u+e06f-e073, u+e075, u+e085-e086
}

@font-face {
    font-family: "Font Awesome 5 Pro";
    font-display: block;
    font-weight: 900;
    src: url(../webfonts/pro-fa-solid-900-8.woff2) format("woff2"), url(../webfonts/pro-fa-solid-900-8.ttf) format("truetype");
    unicode-range: u+f80b
}

@font-face {
    font-family: "Font Awesome 5 Pro";
    font-display: block;
    font-weight: 900;
    src: url(../webfonts/pro-fa-solid-900-11.woff2) format("woff2"), url(../webfonts/pro-fa-solid-900-11.ttf) format("truetype");
    unicode-range: u+f8bc
}

@font-face {
    font-family: "Font Awesome 5 Pro";
    font-display: block;
    font-weight: 900;
    src: url(../webfonts/pro-fa-solid-900-12.woff2) format("woff2"), url(../webfonts/pro-fa-solid-900-12.ttf) format("truetype");
    unicode-range: u+f000, u+f002, u+f009-f00b, u+f00d-f00e, u+f010, u+f013, u+f01e, u+f021-f022, u+f026-f029, u+f02b, u+f032-f039, u+f03b-f03c, u+f042-f044, u+f047-f049, u+f050-f05a, u+f05e, u+f066, u+f06a, u+f070-f071, u+f073-f074, u+f076, u+f079-f07a, u+f07c-f07e, u+f080, u+f083, u+f085, u+f089, u+f08b, u+f08d-f08e, u+f090, u+f094, u+f098, u+f09e, u+f0a0-f0a1
}

@font-face {
    font-family: "Font Awesome 5 Pro";
    font-display: block;
    font-weight: 900;
    src: url(../webfonts/pro-fa-solid-900-13.woff2) format("woff2"), url(../webfonts/pro-fa-solid-900-13.ttf) format("truetype");
    unicode-range: u+f0a4-f0a5, u+f0a7-f0ab, u+f0ae, u+f0b2, u+f0c3-f0c4, u+f0c7, u+f0ca-f0cb, u+f0cd, u+f0d0, u+f0d8-f0db, u+f0dd-f0de, u+f0e2-f0e3, u+f0e9-f0ea, u+f0ec-f0ee, u+f0f0-f0f2, u+f0f4, u+f0f9-f0fe, u+f100-f105, u+f107, u+f10a-f10b, u+f10d, u+f118-f11b, u+f120, u+f122, u+f124-f125, u+f127, u+f12b, u+f134, u+f137-f13a, u+f13e, u+f141-f144, u+f146, u+f148-f14d, u+f150-f154, u+f156-f159, u+f15c-f15e, u+f160-f163, u+f165, u+f175-f178, u+f182-f183, u+f185, u+f187, u+f191-f193, u+f195, u+f197, u+f199, u+f19c-f19d, u+f1ac, u+f1b0, u+f1b3, u+f1b9-f1ba, u+f1c4-f1c9, u+f1cd, u+f1da, u+f1dd-f1de, u+f1e0-f1e3, u+f381-f382
}

@font-face {
    font-family: "Font Awesome 5 Pro";
    font-display: block;
    font-weight: 900;
    src: url(../webfonts/pro-fa-solid-900-14.woff2) format("woff2"), url(../webfonts/pro-fa-solid-900-14.ttf) format("truetype");
    unicode-range: u+f1f6, u+f1fb-f1fe, u+f200-f201, u+f204, u+f206-f207, u+f20b, u+f218, u+f21e, u+f221-f22c, u+f235-f236, u+f239, u+f240-f245, u+f247-f249, u+f24e, u+f252-f25c, u+f26c, u+f271-f274, u+f276-f277, u+f279-f27a, u+f28b, u+f28d, u+f290-f291, u+f29a, u+f29d, u+f2a1-f2a4, u+f2a8, u+f2b6, u+f2b9, u+f2bd, u+f2c1-f2c2, u+f2c7-f2cb, u+f2ce, u+f2d0-f2d1, u+f2d3, u+f2dc, u+f2e2-f2eb, u+f2ed-f2ee, u+f2f0-f2f6, u+f2f8-f2fb, u+f2fd-f2fe, u+f300-f301, u+f304-f308, u+f4e6, u+f8e5
}

@font-face {
    font-family: "Font Awesome 5 Pro";
    font-display: block;
    font-weight: 900;
    src: url(../webfonts/pro-fa-solid-900-15.woff2) format("woff2"), url(../webfonts/pro-fa-solid-900-15.ttf) format("truetype");
    unicode-range: u+f309-f315, u+f317-f319, u+f31c-f31e, u+f320-f327, u+f329-f32e, u+f330-f331, u+f333-f334, u+f336-f33e, u+f340-f34e, u+f350-f35b, u+f35d, u+f360-f362, u+f364-f367, u+f376-f377, u+f386-f387, u+f389-f38a, u+f39b-f39c, u+f3a0, u+f3a5, u+f3b3, u+f3be-f3bf, u+f3c1-f3c2, u+f3c9, u+f3cd, u+f3cf, u+f3d1, u+f3dd-f3de, u+f3e0, u+f3ed, u+f3f0-f3f2, u+f3fa, u+f3fc, u+f3ff-f401, u+f406, u+f40f-f410, u+f422, u+f424, u+f432-f434, u+f436-f444
}

@font-face {
    font-family: "Font Awesome 5 Pro";
    font-display: block;
    font-weight: 900;
    src: url(../webfonts/pro-fa-solid-900-16.woff2) format("woff2"), url(../webfonts/pro-fa-solid-900-16.ttf) format("truetype");
    unicode-range: u+f445-f44a, u+f44c, u+f44e-f451, u+f453-f456, u+f458, u+f45a-f47c, u+f47e-f480, u+f482-f489, u+f48b-f48e, u+f491-f492, u+f495-f497, u+f499-f4b6, u+f4b8-f4c9, u+f4cb, u+f4cd-f4d0, u+f4d2-f4d4, u+f4d6, u+f4d9-f4e1, u+f4e3, u+f4fa-f500
}

@font-face {
    font-family: "Font Awesome 5 Pro";
    font-display: block;
    font-weight: 900;
    src: url(../webfonts/pro-fa-solid-900-17.woff2) format("woff2"), url(../webfonts/pro-fa-solid-900-17.ttf) format("truetype");
    unicode-range: u+f501-f502, u+f504-f507, u+f509, u+f515-f51a, u+f51c, u+f51f-f520, u+f523-f52a, u+f52d-f52f, u+f532-f534, u+f537-f53d, u+f53f-f540, u+f542, u+f546-f547, u+f54a-f54d, u+f54f-f554, u+f556-f558, u+f55a-f55c, u+f55e-f563, u+f565-f56b, u+f56d, u+f571-f576, u+f579-f58f, u+f591, u+f593, u+f596-f59d, u+f59f-f5a0, u+f5a4-f5a7, u+f5a9, u+f5ac
}

@font-face {
    font-family: "Font Awesome 5 Pro";
    font-display: block;
    font-weight: 900;
    src: url(../webfonts/pro-fa-solid-900-18.woff2) format("woff2"), url(../webfonts/pro-fa-solid-900-18.ttf) format("truetype");
    unicode-range: u+f0e4, u+f3fd, u+f5ad-f5af, u+f5b1, u+f5b3-f5b4, u+f5b6, u+f5b8-f5b9, u+f5bb-f5bd, u+f5c0-f5c5, u+f5c7-f5c9, u+f5cb, u+f5cd-f5ce, u+f5d0-f5da, u+f5dd-f5ee, u+f5f0, u+f5f3-f5f5, u+f5f8-f5f9, u+f5fc, u+f5fe-f60e, u+f610-f611, u+f613-f620, u+f622-f63a, u+f63c-f63e, u+f640-f641, u+f643-f648, u+f64b-f64e, u+f650-f652, u+f655, u+f657-f65a
}

@font-face {
    font-family: "Font Awesome 5 Pro";
    font-display: block;
    font-weight: 900;
    src: url(../webfonts/pro-fa-solid-900-19.woff2) format("woff2"), url(../webfonts/pro-fa-solid-900-19.ttf) format("truetype");
    unicode-range: u+f65c-f663, u+f665-f672, u+f674, u+f676-f67c, u+f67e, u+f680-f694, u+f696-f69b, u+f69e, u+f6a0-f6a7, u+f6a9-f6ae, u+f6b0-f6be, u+f6c1-f6c8, u+f6cb, u+f6cd-f6d4, u+f6d6-f6db, u+f6dd-f6e2, u+f6e4-f6e8, u+f6ea-f6f4, u+f6f6-f6f9, u+f6fb
}

@font-face {
    font-family: "Font Awesome 5 Pro";
    font-display: block;
    font-weight: 900;
    src: url(../webfonts/pro-fa-solid-900-20.woff2) format("woff2"), url(../webfonts/pro-fa-solid-900-20.ttf) format("truetype");
    unicode-range: u+f6fc-f6fe, u+f701-f703, u+f705-f70a, u+f70c-f70d, u+f70f-f714, u+f716-f71a, u+f71c-f72d, u+f732, u+f735-f746, u+f748-f754, u+f756, u+f758-f75b, u+f75e-f761, u+f763-f772, u+f774-f777, u+f779, u+f77d-f780, u+f782-f783, u+f786-f787, u+f78a-f78c, u+f78e-f78f, u+f792-f796, u+f79a-f7a8
}

@font-face {
    font-family: "Font Awesome 5 Pro";
    font-display: block;
    font-weight: 900;
    src: url(../webfonts/pro-fa-solid-900-21.woff2) format("woff2"), url(../webfonts/pro-fa-solid-900-21.ttf) format("truetype");
    unicode-range: u+f7a9-f7ae, u+f7b4-f7b5, u+f7b7-f7ba, u+f7be-f7c5, u+f7c7-f7d2, u+f7d4, u+f7d7-f7de, u+f7e2, u+f7e4-f7ed, u+f7ef-f7fe, u+f800, u+f802-f803, u+f805-f809, u+f80d-f812, u+f815-f82e, u+f831-f833, u+f83e, u+f843-f844, u+f847-f84f, u+f851-f854, u+f856-f857, u+f85a-f85b, u+f85d-f863
}

@font-face {
    font-family: "Font Awesome 5 Pro";
    font-display: block;
    font-weight: 900;
    src: url(../webfonts/pro-fa-solid-900-22.woff2) format("woff2"), url(../webfonts/pro-fa-solid-900-22.ttf) format("truetype");
    unicode-range: u+f864-f865, u+f867-f86c, u+f86e-f870, u+f872-f874, u+f876-f892, u+f895-f896, u+f898-f89d, u+f8a0-f8a5, u+f8a7-f8a8, u+f8aa-f8b0, u+f8b3-f8ba, u+f8bd-f8c6, u+f8c8-f8c9, u+f8cb-f8d1, u+f8d3-f8d5, u+f8d8, u+f8da-f8de, u+f8e2-f8e4, u+f8e6, u+f8e9-f8ed, u+f8f0-f8fc, u+f8fe-f8ff
}

@font-face {
    font-family: "Font Awesome 5 Pro";
    font-display: block;
    font-weight: 400;
    src: url(../webfonts/pro-fa-regular-400-0.woff2) format("woff2"), url(../webfonts/pro-fa-regular-400-0.ttf) format("truetype");
    unicode-range: u+e010, u+e017, u+e01b, u+e01f-e021, u+e024, u+e02f, u+e03a, u+e042, u+e045-e046, u+e060, u+e068, u+e06e, u+e074, u+e076, u+f001, u+f004-f005, u+f007-f008, u+f00c, u+f011-f012, u+f015, u+f017-f019, u+f01c, u+f023-f025, u+f02a, u+f02c-f031, u+f03a, u+f03d-f03e, u+f041, u+f04a-f04e, u+f05b, u+f060-f065, u+f067-f068, u+f06b-f06e, u+f072, u+f075, u+f077-f078, u+f07b, u+f084, u+f086, u+f091, u+f093, u+f095, u+f09c-f09d, u+f0a3, u+f0a6, u+f0ac-f0ad, u+f0b0-f0b1, u+f0c0-f0c2, u+f0c5-f0c6, u+f0c8, u+f128, u+f12a, u+f155, u+f292, u+f295, u+f332, u+f541, u+f80a, u+f80c
}

@font-face {
    font-family: "Font Awesome 5 Pro";
    font-display: block;
    font-weight: 400;
    src: url(../webfonts/pro-fa-regular-400-1.woff2) format("woff2"), url(../webfonts/pro-fa-regular-400-1.ttf) format("truetype");
    unicode-range: u+f040, u+f0c9, u+f0cc, u+f0ce, u+f0d1, u+f0d6-f0d7, u+f0dc, u+f0e0, u+f0e7-f0e8, u+f0eb, u+f0f3, u+f0f8, u+f106, u+f108-f109, u+f10e, u+f110-f111, u+f11c, u+f11e, u+f121, u+f126, u+f129, u+f12c-f12e, u+f130-f133, u+f135, u+f13d, u+f140, u+f145, u+f14e, u+f15b, u+f164, u+f186, u+f188, u+f1ab, u+f1ad-f1ae, u+f1b2, u+f1b8, u+f1bb, u+f1c0-f1c3, u+f1ce, u+f1d8, u+f1dc, u+f1e4-f1e6, u+f1ea-f1ec, u+f1f8-f1f9, u+f205, u+f20a, u+f217, u+f219-f21d, u+f22d, u+f233-f234, u+f238, u+f246, u+f24d, u+f251, u+f25d, u+f275, u+f29e, u+f2a0, u+f2a7, u+f2b5, u+f2bb, u+f2cc-f2cd, u+f2d2, u+f2db, u+f2e1, u+f2ec, u+f2f7, u+f2fc, u+f302-f303, u+f316, u+f31a, u+f328, u+f335, u+f363, u+f37e, u+f390, u+f3c5, u+f3ce, u+f3e5, u+f3f4, u+f3fb, u+f40e, u+f435, u+f47d
}

@font-face {
    font-family: "Font Awesome 5 Pro";
    font-display: block;
    font-weight: 400;
    src: url(../webfonts/pro-fa-regular-400-2.woff2) format("woff2"), url(../webfonts/pro-fa-regular-400-2.ttf) format("truetype");
    unicode-range: u+f44b, u+f481, u+f48a, u+f48f-f490, u+f493-f494, u+f498, u+f4b7, u+f4ca, u+f4cc, u+f4d1, u+f4d7-f4d8, u+f4e2, u+f503, u+f508, u+f51b, u+f51d-f51e, u+f521-f522, u+f52b, u+f530, u+f535, u+f53e, u+f543-f545, u+f548-f549, u+f54e, u+f555, u+f559, u+f55d, u+f564, u+f56c, u+f56e-f570, u+f577-f578, u+f590, u+f594-f595, u+f5a1-f5a2, u+f5aa-f5ab, u+f5b0, u+f5b7, u+f5ba, u+f5bf, u+f5ca, u+f5db-f5dc, u+f5ef, u+f5f2, u+f5f6, u+f5fb, u+f5fd, u+f621, u+f63b, u+f649-f64a, u+f64f, u+f653-f654, u+f656, u+f65b, u+f664, u+f673, u+f675, u+f67d, u+f67f, u+f695, u+f69c, u+f6a8, u+f6bf-f6c0, u+f6d5, u+f6e3, u+f6e9, u+f6f5, u+f6fa, u+f6ff-f700, u+f70b, u+f70e, u+f715, u+f71b, u+f72e-f72f, u+f733-f734, u+f747, u+f755, u+f757, u+f75c, u+f762, u+f773, u+f77c, u+f781, u+f784, u+f788, u+f7b2, u+f7b6, u+f7bd, u+f7d5, u+f7ee, u+f7ff, u+f801, u+f804, u+f813-f814, u+f82f-f830, u+f845-f846, u+f850, u+f855, u+f858-f859, u+f85c, u+f866, u+f86d, u+f871, u+f875, u+f893-f894, u+f897, u+f89f, u+f8a9, u+f8b1-f8b2, u+f8bb, u+f8c7, u+f8d6-f8d7, u+f8d9
}

@font-face {
    font-family: "Font Awesome 5 Pro";
    font-display: block;
    font-weight: 400;
    src: url(../webfonts/pro-fa-regular-400-3.woff2) format("woff2"), url(../webfonts/pro-fa-regular-400-3.ttf) format("truetype");
    unicode-range: u+e000-e006, u+e008-e00f, u+e011-e012, u+e014-e016, u+e018-e019, u+e01c-e01d, u+e022-e023, u+e025-e02e, u+e030-e039, u+e03b-e041, u+e043-e044, u+e047-e048, u+e04a-e051, u+e053-e054, u+e058-e05f, u+f069, u+f1fa, u+f52c, u+f531, u+f536, u+f69f, u+f8df-f8e0, u+f8e7, u+f8ee-f8ef, u+f8fd
}

@font-face {
    font-family: "Font Awesome 5 Pro";
    font-display: block;
    font-weight: 400;
    src: url(../webfonts/pro-fa-regular-400-4.woff2) format("woff2"), url(../webfonts/pro-fa-regular-400-4.ttf) format("truetype");
    unicode-range: u+e061-e067, u+e069-e06d, u+e06f-e073, u+e075, u+e085-e086
}

@font-face {
    font-family: "Font Awesome 5 Pro";
    font-display: block;
    font-weight: 400;
    src: url(../webfonts/pro-fa-regular-400-8.woff2) format("woff2"), url(../webfonts/pro-fa-regular-400-8.ttf) format("truetype");
    unicode-range: u+f80b
}

@font-face {
    font-family: "Font Awesome 5 Pro";
    font-display: block;
    font-weight: 400;
    src: url(../webfonts/pro-fa-regular-400-11.woff2) format("woff2"), url(../webfonts/pro-fa-regular-400-11.ttf) format("truetype");
    unicode-range: u+f8bc
}

@font-face {
    font-family: "Font Awesome 5 Pro";
    font-display: block;
    font-weight: 400;
    src: url(../webfonts/pro-fa-regular-400-12.woff2) format("woff2"), url(../webfonts/pro-fa-regular-400-12.ttf) format("truetype");
    unicode-range: u+f000, u+f002, u+f009-f00b, u+f00d-f00e, u+f010, u+f013, u+f01e, u+f021-f022, u+f026-f029, u+f02b, u+f032-f039, u+f03b-f03c, u+f042-f044, u+f047-f049, u+f050-f05a, u+f05e, u+f066, u+f06a, u+f070-f071, u+f073-f074, u+f076, u+f079-f07a, u+f07c-f07e, u+f080, u+f083, u+f085, u+f089, u+f08b, u+f08d-f08e, u+f090, u+f094, u+f098, u+f09e, u+f0a0-f0a1
}

@font-face {
    font-family: "Font Awesome 5 Pro";
    font-display: block;
    font-weight: 400;
    src: url(../webfonts/pro-fa-regular-400-13.woff2) format("woff2"), url(../webfonts/pro-fa-regular-400-13.ttf) format("truetype");
    unicode-range: u+f0a4-f0a5, u+f0a7-f0ab, u+f0ae, u+f0b2, u+f0c3-f0c4, u+f0c7, u+f0ca-f0cb, u+f0cd, u+f0d0, u+f0d8-f0db, u+f0dd-f0de, u+f0e2-f0e3, u+f0e9-f0ea, u+f0ec-f0ee, u+f0f0-f0f2, u+f0f4, u+f0f9-f0fe, u+f100-f105, u+f107, u+f10a-f10b, u+f10d, u+f118-f11b, u+f120, u+f122, u+f124-f125, u+f127, u+f12b, u+f134, u+f137-f13a, u+f13e, u+f141-f144, u+f146, u+f148-f14d, u+f150-f154, u+f156-f159, u+f15c-f15e, u+f160-f163, u+f165, u+f175-f178, u+f182-f183, u+f185, u+f187, u+f191-f193, u+f195, u+f197, u+f199, u+f19c-f19d, u+f1ac, u+f1b0, u+f1b3, u+f1b9-f1ba, u+f1c4-f1c9, u+f1cd, u+f1da, u+f1dd-f1de, u+f1e0-f1e3, u+f381-f382
}

@font-face {
    font-family: "Font Awesome 5 Pro";
    font-display: block;
    font-weight: 400;
    src: url(../webfonts/pro-fa-regular-400-14.woff2) format("woff2"), url(../webfonts/pro-fa-regular-400-14.ttf) format("truetype");
    unicode-range: u+f1f6, u+f1fb-f1fe, u+f200-f201, u+f204, u+f206-f207, u+f20b, u+f218, u+f21e, u+f221-f22c, u+f235-f236, u+f239, u+f240-f245, u+f247-f249, u+f24e, u+f252-f25c, u+f26c, u+f271-f274, u+f276-f277, u+f279-f27a, u+f28b, u+f28d, u+f290-f291, u+f29a, u+f29d, u+f2a1-f2a4, u+f2a8, u+f2b6, u+f2b9, u+f2bd, u+f2c1-f2c2, u+f2c7-f2cb, u+f2ce, u+f2d0-f2d1, u+f2d3, u+f2dc, u+f2e2-f2eb, u+f2ed-f2ee, u+f2f0-f2f6, u+f2f8-f2fb, u+f2fd-f2fe, u+f300-f301, u+f304-f308, u+f4e6, u+f8e5
}

@font-face {
    font-family: "Font Awesome 5 Pro";
    font-display: block;
    font-weight: 400;
    src: url(../webfonts/pro-fa-regular-400-15.woff2) format("woff2"), url(../webfonts/pro-fa-regular-400-15.ttf) format("truetype");
    unicode-range: u+f309-f315, u+f317-f319, u+f31c-f31e, u+f320-f327, u+f329-f32e, u+f330-f331, u+f333-f334, u+f336-f33e, u+f340-f34e, u+f350-f35b, u+f35d, u+f360-f362, u+f364-f367, u+f376-f377, u+f386-f387, u+f389-f38a, u+f39b-f39c, u+f3a0, u+f3a5, u+f3b3, u+f3be-f3bf, u+f3c1-f3c2, u+f3c9, u+f3cd, u+f3cf, u+f3d1, u+f3dd-f3de, u+f3e0, u+f3ed, u+f3f0-f3f2, u+f3fa, u+f3fc, u+f3ff-f401, u+f406, u+f40f-f410, u+f422, u+f424, u+f432-f434, u+f436-f444
}

@font-face {
    font-family: "Font Awesome 5 Pro";
    font-display: block;
    font-weight: 400;
    src: url(../webfonts/pro-fa-regular-400-16.woff2) format("woff2"), url(../webfonts/pro-fa-regular-400-16.ttf) format("truetype");
    unicode-range: u+f445-f44a, u+f44c, u+f44e-f451, u+f453-f456, u+f458, u+f45a-f47c, u+f47e-f480, u+f482-f489, u+f48b-f48e, u+f491-f492, u+f495-f497, u+f499-f4b6, u+f4b8-f4c9, u+f4cb, u+f4cd-f4d0, u+f4d2-f4d4, u+f4d6, u+f4d9-f4e1, u+f4e3, u+f4fa-f500
}

@font-face {
    font-family: "Font Awesome 5 Pro";
    font-display: block;
    font-weight: 400;
    src: url(../webfonts/pro-fa-regular-400-17.woff2) format("woff2"), url(../webfonts/pro-fa-regular-400-17.ttf) format("truetype");
    unicode-range: u+f501-f502, u+f504-f507, u+f509, u+f515-f51a, u+f51c, u+f51f-f520, u+f523-f52a, u+f52d-f52f, u+f532-f534, u+f537-f53d, u+f53f-f540, u+f542, u+f546-f547, u+f54a-f54d, u+f54f-f554, u+f556-f558, u+f55a-f55c, u+f55e-f563, u+f565-f56b, u+f56d, u+f571-f576, u+f579-f58f, u+f591, u+f593, u+f596-f59d, u+f59f-f5a0, u+f5a4-f5a7, u+f5a9, u+f5ac
}

@font-face {
    font-family: "Font Awesome 5 Pro";
    font-display: block;
    font-weight: 400;
    src: url(../webfonts/pro-fa-regular-400-18.woff2) format("woff2"), url(../webfonts/pro-fa-regular-400-18.ttf) format("truetype");
    unicode-range: u+f0e4, u+f3fd, u+f5ad-f5af, u+f5b1, u+f5b3-f5b4, u+f5b6, u+f5b8-f5b9, u+f5bb-f5bd, u+f5c0-f5c5, u+f5c7-f5c9, u+f5cb, u+f5cd-f5ce, u+f5d0-f5da, u+f5dd-f5ee, u+f5f0, u+f5f3-f5f5, u+f5f8-f5f9, u+f5fc, u+f5fe-f60e, u+f610-f611, u+f613-f620, u+f622-f63a, u+f63c-f63e, u+f640-f641, u+f643-f648, u+f64b-f64e, u+f650-f652, u+f655, u+f657-f65a
}

@font-face {
    font-family: "Font Awesome 5 Pro";
    font-display: block;
    font-weight: 400;
    src: url(../webfonts/pro-fa-regular-400-19.woff2) format("woff2"), url(../webfonts/pro-fa-regular-400-19.ttf) format("truetype");
    unicode-range: u+f65c-f663, u+f665-f672, u+f674, u+f676-f67c, u+f67e, u+f680-f694, u+f696-f69b, u+f69e, u+f6a0-f6a7, u+f6a9-f6ae, u+f6b0-f6be, u+f6c1-f6c8, u+f6cb, u+f6cd-f6d4, u+f6d6-f6db, u+f6dd-f6e2, u+f6e4-f6e8, u+f6ea-f6f4, u+f6f6-f6f9, u+f6fb
}

@font-face {
    font-family: "Font Awesome 5 Pro";
    font-display: block;
    font-weight: 400;
    src: url(../webfonts/pro-fa-regular-400-20.woff2) format("woff2"), url(../webfonts/pro-fa-regular-400-20.ttf) format("truetype");
    unicode-range: u+f6fc-f6fe, u+f701-f703, u+f705-f70a, u+f70c-f70d, u+f70f-f714, u+f716-f71a, u+f71c-f72d, u+f732, u+f735-f746, u+f748-f754, u+f756, u+f758-f75b, u+f75e-f761, u+f763-f772, u+f774-f777, u+f779, u+f77d-f780, u+f782-f783, u+f786-f787, u+f78a-f78c, u+f78e-f78f, u+f792-f796, u+f79a-f7a8
}

@font-face {
    font-family: "Font Awesome 5 Pro";
    font-display: block;
    font-weight: 400;
    src: url(../webfonts/pro-fa-regular-400-21.woff2) format("woff2"), url(../webfonts/pro-fa-regular-400-21.ttf) format("truetype");
    unicode-range: u+f7a9-f7ae, u+f7b4-f7b5, u+f7b7-f7ba, u+f7be-f7c5, u+f7c7-f7d2, u+f7d4, u+f7d7-f7de, u+f7e2, u+f7e4-f7ed, u+f7ef-f7fe, u+f800, u+f802-f803, u+f805-f809, u+f80d-f812, u+f815-f82e, u+f831-f833, u+f83e, u+f843-f844, u+f847-f84f, u+f851-f854, u+f856-f857, u+f85a-f85b, u+f85d-f863
}

@font-face {
    font-family: "Font Awesome 5 Pro";
    font-display: block;
    font-weight: 400;
    src: url(../webfonts/pro-fa-regular-400-22.woff2) format("woff2"), url(../webfonts/pro-fa-regular-400-22.ttf) format("truetype");
    unicode-range: u+f864-f865, u+f867-f86c, u+f86e-f870, u+f872-f874, u+f876-f892, u+f895-f896, u+f898-f89d, u+f8a0-f8a5, u+f8a7-f8a8, u+f8aa-f8b0, u+f8b3-f8ba, u+f8bd-f8c6, u+f8c8-f8c9, u+f8cb-f8d1, u+f8d3-f8d5, u+f8d8, u+f8da-f8de, u+f8e2-f8e4, u+f8e6, u+f8e9-f8ed, u+f8f0-f8fc, u+f8fe-f8ff
}

@font-face {
    font-family: "Font Awesome 5 Pro";
    font-display: block;
    font-weight: 300;
    src: url(../webfonts/pro-fa-light-300-0.woff2) format("woff2"), url(../webfonts/pro-fa-light-300-0.ttf) format("truetype");
    unicode-range: u+e010, u+e017, u+e01b, u+e01f-e021, u+e024, u+e02f, u+e03a, u+e042, u+e045-e046, u+e060, u+e068, u+e06e, u+e074, u+e076, u+f001, u+f004-f005, u+f007-f008, u+f00c, u+f011-f012, u+f015, u+f017-f019, u+f01c, u+f023-f025, u+f02a, u+f02c-f031, u+f03a, u+f03d-f03e, u+f041, u+f04a-f04e, u+f05b, u+f060-f065, u+f067-f068, u+f06b-f06e, u+f072, u+f075, u+f077-f078, u+f07b, u+f084, u+f086, u+f091, u+f093, u+f095, u+f09c-f09d, u+f0a3, u+f0a6, u+f0ac-f0ad, u+f0b0-f0b1, u+f0c0-f0c2, u+f0c5-f0c6, u+f0c8, u+f128, u+f12a, u+f155, u+f292, u+f295, u+f332, u+f541, u+f80a, u+f80c
}

@font-face {
    font-family: "Font Awesome 5 Pro";
    font-display: block;
    font-weight: 300;
    src: url(../webfonts/pro-fa-light-300-1.woff2) format("woff2"), url(../webfonts/pro-fa-light-300-1.ttf) format("truetype");
    unicode-range: u+f040, u+f0c9, u+f0cc, u+f0ce, u+f0d1, u+f0d6-f0d7, u+f0dc, u+f0e0, u+f0e7-f0e8, u+f0eb, u+f0f3, u+f0f8, u+f106, u+f108-f109, u+f10e, u+f110-f111, u+f11c, u+f11e, u+f121, u+f126, u+f129, u+f12c-f12e, u+f130-f133, u+f135, u+f13d, u+f140, u+f145, u+f14e, u+f15b, u+f164, u+f186, u+f188, u+f1ab, u+f1ad-f1ae, u+f1b2, u+f1b8, u+f1bb, u+f1c0-f1c3, u+f1ce, u+f1d8, u+f1dc, u+f1e4-f1e6, u+f1ea-f1ec, u+f1f8-f1f9, u+f205, u+f20a, u+f217, u+f219-f21d, u+f22d, u+f233-f234, u+f238, u+f246, u+f24d, u+f251, u+f25d, u+f275, u+f29e, u+f2a0, u+f2a7, u+f2b5, u+f2bb, u+f2cc-f2cd, u+f2d2, u+f2db, u+f2e1, u+f2ec, u+f2f7, u+f2fc, u+f302-f303, u+f316, u+f31a, u+f328, u+f335, u+f363, u+f37e, u+f390, u+f3c5, u+f3ce, u+f3e5, u+f3f4, u+f3fb, u+f40e, u+f435, u+f47d
}

@font-face {
    font-family: "Font Awesome 5 Pro";
    font-display: block;
    font-weight: 300;
    src: url(../webfonts/pro-fa-light-300-2.woff2) format("woff2"), url(../webfonts/pro-fa-light-300-2.ttf) format("truetype");
    unicode-range: u+f44b, u+f481, u+f48a, u+f48f-f490, u+f493-f494, u+f498, u+f4b7, u+f4ca, u+f4cc, u+f4d1, u+f4d7-f4d8, u+f4e2, u+f503, u+f508, u+f51b, u+f51d-f51e, u+f521-f522, u+f52b, u+f530, u+f535, u+f53e, u+f543-f545, u+f548-f549, u+f54e, u+f555, u+f559, u+f55d, u+f564, u+f56c, u+f56e-f570, u+f577-f578, u+f590, u+f594-f595, u+f5a1-f5a2, u+f5aa-f5ab, u+f5b0, u+f5b7, u+f5ba, u+f5bf, u+f5ca, u+f5db-f5dc, u+f5ef, u+f5f2, u+f5f6, u+f5fb, u+f5fd, u+f621, u+f63b, u+f649-f64a, u+f64f, u+f653-f654, u+f656, u+f65b, u+f664, u+f673, u+f675, u+f67d, u+f67f, u+f695, u+f69c, u+f6a8, u+f6bf-f6c0, u+f6d5, u+f6e3, u+f6e9, u+f6f5, u+f6fa, u+f6ff-f700, u+f70b, u+f70e, u+f715, u+f71b, u+f72e-f72f, u+f733-f734, u+f747, u+f755, u+f757, u+f75c, u+f762, u+f773, u+f77c, u+f781, u+f784, u+f788, u+f7b2, u+f7b6, u+f7bd, u+f7d5, u+f7ee, u+f7ff, u+f801, u+f804, u+f813-f814, u+f82f-f830, u+f845-f846, u+f850, u+f855, u+f858-f859, u+f85c, u+f866, u+f86d, u+f871, u+f875, u+f893-f894, u+f897, u+f89f, u+f8a9, u+f8b1-f8b2, u+f8bb, u+f8c7, u+f8d6-f8d7, u+f8d9
}

@font-face {
    font-family: "Font Awesome 5 Pro";
    font-display: block;
    font-weight: 300;
    src: url(../webfonts/pro-fa-light-300-3.woff2) format("woff2"), url(../webfonts/pro-fa-light-300-3.ttf) format("truetype");
    unicode-range: u+e000-e006, u+e008-e00f, u+e011-e012, u+e014-e016, u+e018-e019, u+e01c-e01d, u+e022-e023, u+e025-e02e, u+e030-e039, u+e03b-e041, u+e043-e044, u+e047-e048, u+e04a-e051, u+e053-e054, u+e058-e05f, u+f069, u+f1fa, u+f52c, u+f531, u+f536, u+f69f, u+f8df-f8e0, u+f8e7, u+f8ee-f8ef, u+f8fd
}

@font-face {
    font-family: "Font Awesome 5 Pro";
    font-display: block;
    font-weight: 300;
    src: url(../webfonts/pro-fa-light-300-4.woff2) format("woff2"), url(../webfonts/pro-fa-light-300-4.ttf) format("truetype");
    unicode-range: u+e061-e067, u+e069-e06d, u+e06f-e073, u+e075, u+e085-e086
}

@font-face {
    font-family: "Font Awesome 5 Pro";
    font-display: block;
    font-weight: 300;
    src: url(../webfonts/pro-fa-light-300-8.woff2) format("woff2"), url(../webfonts/pro-fa-light-300-8.ttf) format("truetype");
    unicode-range: u+f80b
}

@font-face {
    font-family: "Font Awesome 5 Pro";
    font-display: block;
    font-weight: 300;
    src: url(../webfonts/pro-fa-light-300-11.woff2) format("woff2"), url(../webfonts/pro-fa-light-300-11.ttf) format("truetype");
    unicode-range: u+f8bc
}

@font-face {
    font-family: "Font Awesome 5 Pro";
    font-display: block;
    font-weight: 300;
    src: url(../webfonts/pro-fa-light-300-12.woff2) format("woff2"), url(../webfonts/pro-fa-light-300-12.ttf) format("truetype");
    unicode-range: u+f000, u+f002, u+f009-f00b, u+f00d-f00e, u+f010, u+f013, u+f01e, u+f021-f022, u+f026-f029, u+f02b, u+f032-f039, u+f03b-f03c, u+f042-f044, u+f047-f049, u+f050-f05a, u+f05e, u+f066, u+f06a, u+f070-f071, u+f073-f074, u+f076, u+f079-f07a, u+f07c-f07e, u+f080, u+f083, u+f085, u+f089, u+f08b, u+f08d-f08e, u+f090, u+f094, u+f098, u+f09e, u+f0a0-f0a1
}

@font-face {
    font-family: "Font Awesome 5 Pro";
    font-display: block;
    font-weight: 300;
    src: url(../webfonts/pro-fa-light-300-13.woff2) format("woff2"), url(../webfonts/pro-fa-light-300-13.ttf) format("truetype");
    unicode-range: u+f0a4-f0a5, u+f0a7-f0ab, u+f0ae, u+f0b2, u+f0c3-f0c4, u+f0c7, u+f0ca-f0cb, u+f0cd, u+f0d0, u+f0d8-f0db, u+f0dd-f0de, u+f0e2-f0e3, u+f0e9-f0ea, u+f0ec-f0ee, u+f0f0-f0f2, u+f0f4, u+f0f9-f0fe, u+f100-f105, u+f107, u+f10a-f10b, u+f10d, u+f118-f11b, u+f120, u+f122, u+f124-f125, u+f127, u+f12b, u+f134, u+f137-f13a, u+f13e, u+f141-f144, u+f146, u+f148-f14d, u+f150-f154, u+f156-f159, u+f15c-f15e, u+f160-f163, u+f165, u+f175-f178, u+f182-f183, u+f185, u+f187, u+f191-f193, u+f195, u+f197, u+f199, u+f19c-f19d, u+f1ac, u+f1b0, u+f1b3, u+f1b9-f1ba, u+f1c4-f1c9, u+f1cd, u+f1da, u+f1dd-f1de, u+f1e0-f1e3, u+f381-f382
}

@font-face {
    font-family: "Font Awesome 5 Pro";
    font-display: block;
    font-weight: 300;
    src: url(../webfonts/pro-fa-light-300-14.woff2) format("woff2"), url(../webfonts/pro-fa-light-300-14.ttf) format("truetype");
    unicode-range: u+f1f6, u+f1fb-f1fe, u+f200-f201, u+f204, u+f206-f207, u+f20b, u+f218, u+f21e, u+f221-f22c, u+f235-f236, u+f239, u+f240-f245, u+f247-f249, u+f24e, u+f252-f25c, u+f26c, u+f271-f274, u+f276-f277, u+f279-f27a, u+f28b, u+f28d, u+f290-f291, u+f29a, u+f29d, u+f2a1-f2a4, u+f2a8, u+f2b6, u+f2b9, u+f2bd, u+f2c1-f2c2, u+f2c7-f2cb, u+f2ce, u+f2d0-f2d1, u+f2d3, u+f2dc, u+f2e2-f2eb, u+f2ed-f2ee, u+f2f0-f2f6, u+f2f8-f2fb, u+f2fd-f2fe, u+f300-f301, u+f304-f308, u+f4e6, u+f8e5
}

@font-face {
    font-family: "Font Awesome 5 Pro";
    font-display: block;
    font-weight: 300;
    src: url(../webfonts/pro-fa-light-300-15.woff2) format("woff2"), url(../webfonts/pro-fa-light-300-15.ttf) format("truetype");
    unicode-range: u+f309-f315, u+f317-f319, u+f31c-f31e, u+f320-f327, u+f329-f32e, u+f330-f331, u+f333-f334, u+f336-f33e, u+f340-f34e, u+f350-f35b, u+f35d, u+f360-f362, u+f364-f367, u+f376-f377, u+f386-f387, u+f389-f38a, u+f39b-f39c, u+f3a0, u+f3a5, u+f3b3, u+f3be-f3bf, u+f3c1-f3c2, u+f3c9, u+f3cd, u+f3cf, u+f3d1, u+f3dd-f3de, u+f3e0, u+f3ed, u+f3f0-f3f2, u+f3fa, u+f3fc, u+f3ff-f401, u+f406, u+f40f-f410, u+f422, u+f424, u+f432-f434, u+f436-f444
}

@font-face {
    font-family: "Font Awesome 5 Pro";
    font-display: block;
    font-weight: 300;
    src: url(../webfonts/pro-fa-light-300-16.woff2) format("woff2"), url(../webfonts/pro-fa-light-300-16.ttf) format("truetype");
    unicode-range: u+f445-f44a, u+f44c, u+f44e-f451, u+f453-f456, u+f458, u+f45a-f47c, u+f47e-f480, u+f482-f489, u+f48b-f48e, u+f491-f492, u+f495-f497, u+f499-f4b6, u+f4b8-f4c9, u+f4cb, u+f4cd-f4d0, u+f4d2-f4d4, u+f4d6, u+f4d9-f4e1, u+f4e3, u+f4fa-f500
}

@font-face {
    font-family: "Font Awesome 5 Pro";
    font-display: block;
    font-weight: 300;
    src: url(../webfonts/pro-fa-light-300-17.woff2) format("woff2"), url(../webfonts/pro-fa-light-300-17.ttf) format("truetype");
    unicode-range: u+f501-f502, u+f504-f507, u+f509, u+f515-f51a, u+f51c, u+f51f-f520, u+f523-f52a, u+f52d-f52f, u+f532-f534, u+f537-f53d, u+f53f-f540, u+f542, u+f546-f547, u+f54a-f54d, u+f54f-f554, u+f556-f558, u+f55a-f55c, u+f55e-f563, u+f565-f56b, u+f56d, u+f571-f576, u+f579-f58f, u+f591, u+f593, u+f596-f59d, u+f59f-f5a0, u+f5a4-f5a7, u+f5a9, u+f5ac
}

@font-face {
    font-family: "Font Awesome 5 Pro";
    font-display: block;
    font-weight: 300;
    src: url(../webfonts/pro-fa-light-300-18.woff2) format("woff2"), url(../webfonts/pro-fa-light-300-18.ttf) format("truetype");
    unicode-range: u+f0e4, u+f3fd, u+f5ad-f5af, u+f5b1, u+f5b3-f5b4, u+f5b6, u+f5b8-f5b9, u+f5bb-f5bd, u+f5c0-f5c5, u+f5c7-f5c9, u+f5cb, u+f5cd-f5ce, u+f5d0-f5da, u+f5dd-f5ee, u+f5f0, u+f5f3-f5f5, u+f5f8-f5f9, u+f5fc, u+f5fe-f60e, u+f610-f611, u+f613-f620, u+f622-f63a, u+f63c-f63e, u+f640-f641, u+f643-f648, u+f64b-f64e, u+f650-f652, u+f655, u+f657-f65a
}

@font-face {
    font-family: "Font Awesome 5 Pro";
    font-display: block;
    font-weight: 300;
    src: url(../webfonts/pro-fa-light-300-19.woff2) format("woff2"), url(../webfonts/pro-fa-light-300-19.ttf) format("truetype");
    unicode-range: u+f65c-f663, u+f665-f672, u+f674, u+f676-f67c, u+f67e, u+f680-f694, u+f696-f69b, u+f69e, u+f6a0-f6a7, u+f6a9-f6ae, u+f6b0-f6be, u+f6c1-f6c8, u+f6cb, u+f6cd-f6d4, u+f6d6-f6db, u+f6dd-f6e2, u+f6e4-f6e8, u+f6ea-f6f4, u+f6f6-f6f9, u+f6fb
}

@font-face {
    font-family: "Font Awesome 5 Pro";
    font-display: block;
    font-weight: 300;
    src: url(../webfonts/pro-fa-light-300-20.woff2) format("woff2"), url(../webfonts/pro-fa-light-300-20.ttf) format("truetype");
    unicode-range: u+f6fc-f6fe, u+f701-f703, u+f705-f70a, u+f70c-f70d, u+f70f-f714, u+f716-f71a, u+f71c-f72d, u+f732, u+f735-f746, u+f748-f754, u+f756, u+f758-f75b, u+f75e-f761, u+f763-f772, u+f774-f777, u+f779, u+f77d-f780, u+f782-f783, u+f786-f787, u+f78a-f78c, u+f78e-f78f, u+f792-f796, u+f79a-f7a8
}

@font-face {
    font-family: "Font Awesome 5 Pro";
    font-display: block;
    font-weight: 300;
    src: url(../webfonts/pro-fa-light-300-21.woff2) format("woff2"), url(../webfonts/pro-fa-light-300-21.ttf) format("truetype");
    unicode-range: u+f7a9-f7ae, u+f7b4-f7b5, u+f7b7-f7ba, u+f7be-f7c5, u+f7c7-f7d2, u+f7d4, u+f7d7-f7de, u+f7e2, u+f7e4-f7ed, u+f7ef-f7fe, u+f800, u+f802-f803, u+f805-f809, u+f80d-f812, u+f815-f82e, u+f831-f833, u+f83e, u+f843-f844, u+f847-f84f, u+f851-f854, u+f856-f857, u+f85a-f85b, u+f85d-f863
}

@font-face {
    font-family: "Font Awesome 5 Pro";
    font-display: block;
    font-weight: 300;
    src: url(../webfonts/pro-fa-light-300-22.woff2) format("woff2"), url(../webfonts/pro-fa-light-300-22.ttf) format("truetype");
    unicode-range: u+f864-f865, u+f867-f86c, u+f86e-f870, u+f872-f874, u+f876-f892, u+f895-f896, u+f898-f89d, u+f8a0-f8a5, u+f8a7-f8a8, u+f8aa-f8b0, u+f8b3-f8ba, u+f8bd-f8c6, u+f8c8-f8c9, u+f8cb-f8d1, u+f8d3-f8d5, u+f8d8, u+f8da-f8de, u+f8e2-f8e4, u+f8e6, u+f8e9-f8ed, u+f8f0-f8fc, u+f8fe-f8ff
}

@font-face {
    font-family: "Font Awesome 5 Duotone";
    font-display: block;
    font-weight: 900;
    src: url(../webfonts/pro-fa-duotone-900-0.woff2) format("woff2"), url(../webfonts/pro-fa-duotone-900-0.ttf) format("truetype");
    unicode-range: u+e010, u+e017, u+e01b, u+e01f-e021, u+e024, u+e02f, u+e03a, u+e042, u+e045-e046, u+e060, u+e068, u+e06e, u+e074, u+e076, u+f001, u+f004-f005, u+f007-f008, u+f00c, u+f011-f012, u+f015, u+f017-f019, u+f01c, u+f023-f025, u+f02a, u+f02c-f031, u+f03a, u+f03d-f03e, u+f041, u+f04a-f04e, u+f05b, u+f060-f065, u+f067-f068, u+f06b-f06e, u+f072, u+f075, u+f077-f078, u+f07b, u+f084, u+f086, u+f091, u+f093, u+f095, u+f09c-f09d, u+f0a3, u+f128, u+f12a, u+f155, u+f292, u+f295, u+f332, u+f541, u+f80a, u+f80c, u+10e010, u+10e017, u+10e01b, u+10e01f-10e021, u+10e024, u+10e02f, u+10e03a, u+10e042, u+10e045-10e046, u+10e060, u+10e068, u+10e06e, u+10e074, u+10e076, u+10f001, u+10f004-10f005, u+10f007-10f008, u+10f00c, u+10f011-10f012, u+10f015, u+10f017-10f019, u+10f01c, u+10f023-10f025, u+10f02a, u+10f02c-10f031, u+10f03a, u+10f03d-10f03e, u+10f041, u+10f04a-10f04e, u+10f05b, u+10f060-10f065, u+10f067-10f068, u+10f06b-10f06e, u+10f072, u+10f075, u+10f077-10f078, u+10f07b, u+10f084, u+10f086, u+10f091, u+10f093, u+10f095, u+10f09c-10f09d, u+10f0a3, u+10f128, u+10f12a, u+10f155, u+10f292, u+10f295, u+10f332, u+10f541, u+10f80a, u+10f80c
}

@font-face {
    font-family: "Font Awesome 5 Duotone";
    font-display: block;
    font-weight: 900;
    src: url(../webfonts/pro-fa-duotone-900-1.woff2) format("woff2"), url(../webfonts/pro-fa-duotone-900-1.ttf) format("truetype");
    unicode-range: u+f0a6, u+f0ac-f0ad, u+f0b0-f0b1, u+f0c0-f0c2, u+f0c5-f0c6, u+f0c8-f0c9, u+f0cc, u+f0ce, u+f0d1, u+f0d6-f0d7, u+f0dc, u+f0e0, u+f0e7-f0e8, u+f0eb, u+f0f3, u+f0f8, u+f106, u+f109, u+f10e, u+f110-f111, u+f11c, u+f11e, u+f121, u+f126, u+f129, u+f12c-f12e, u+f130-f133, u+f135, u+f13d, u+f140, u+f145, u+f14e, u+f15b, u+f164, u+f186, u+f188, u+f1ab, u+f1ad-f1ae, u+f1b2, u+f1b8, u+f1bb, u+f1c0-f1c3, u+f1ce, u+f1d8, u+f1dc, u+f1e4-f1e6, u+f1ea-f1ec, u+f1f8-f1f9, u+f205, u+f20a, u+f217, u+f219-f21d, u+f22d, u+f233-f234, u+f238, u+f246, u+f24d, u+f251, u+f25d, u+f275, u+f29e, u+f47d, u+10f0a6, u+10f0ac-10f0ad, u+10f0b0-10f0b1, u+10f0c0-10f0c2, u+10f0c5-10f0c6, u+10f0c8-10f0c9, u+10f0cc, u+10f0ce, u+10f0d1, u+10f0d6-10f0d7, u+10f0dc, u+10f0e0, u+10f0e7-10f0e8, u+10f0eb, u+10f0f3, u+10f0f8, u+10f106, u+10f109, u+10f10e, u+10f110-10f111, u+10f11c, u+10f11e, u+10f121, u+10f126, u+10f129, u+10f12c-10f12e, u+10f130-10f133, u+10f135, u+10f13d, u+10f140, u+10f145, u+10f14e, u+10f15b, u+10f164, u+10f186, u+10f188, u+10f1ab, u+10f1ad-10f1ae, u+10f1b2, u+10f1b8, u+10f1bb, u+10f1c0-10f1c3, u+10f1ce, u+10f1d8, u+10f1dc, u+10f1e4-10f1e6, u+10f1ea-10f1ec, u+10f1f8-10f1f9, u+10f205, u+10f20a, u+10f217, u+10f219-10f21d, u+10f22d, u+10f233-10f234, u+10f238, u+10f246, u+10f24d, u+10f251, u+10f25d, u+10f275, u+10f29e, u+10f47d
}

@font-face {
    font-family: "Font Awesome 5 Duotone";
    font-display: block;
    font-weight: 900;
    src: url(../webfonts/pro-fa-duotone-900-2.woff2) format("woff2"), url(../webfonts/pro-fa-duotone-900-2.ttf) format("truetype");
    unicode-range: u+f040, u+f108, u+f2a0, u+f2a7, u+f2b5, u+f2bb, u+f2cc-f2cd, u+f2d2, u+f2db, u+f2e1, u+f2ec, u+f2f7, u+f2fc, u+f302-f303, u+f316, u+f31a, u+f328, u+f335, u+f363, u+f37e, u+f390, u+f3c5, u+f3ce, u+f3e5, u+f3f4, u+f3fb, u+f40e, u+f435, u+f44b, u+f481, u+f48a, u+f48f-f490, u+f493-f494, u+f498, u+f4b7, u+f4ca, u+f4cc, u+f4d1, u+f4d7-f4d8, u+f4e2, u+f503, u+f508, u+f51b, u+f51d-f51e, u+f521-f522, u+f52b, u+f530, u+f535, u+f53e, u+f543-f545, u+f548-f549, u+f54e, u+f555, u+f559, u+f55d, u+f564, u+f56c, u+f56e-f570, u+f577-f578, u+f590, u+f594-f595, u+f5a1-f5a2, u+f5aa-f5ab, u+f5b0, u+f5b7, u+f5ba, u+f5bf, u+f5ca, u+f5db-f5dc, u+f5ef, u+f5f2, u+f5f6, u+f5fb, u+10f040, u+10f108, u+10f2a0, u+10f2a7, u+10f2b5, u+10f2bb, u+10f2cc-10f2cd, u+10f2d2, u+10f2db, u+10f2e1, u+10f2ec, u+10f2f7, u+10f2fc, u+10f302-10f303, u+10f316, u+10f31a, u+10f328, u+10f335, u+10f363, u+10f37e, u+10f390, u+10f3c5, u+10f3ce, u+10f3e5, u+10f3f4, u+10f3fb, u+10f40e, u+10f435, u+10f44b, u+10f481, u+10f48a, u+10f48f-10f490, u+10f493-10f494, u+10f498, u+10f4b7, u+10f4ca, u+10f4cc, u+10f4d1, u+10f4d7-10f4d8, u+10f4e2, u+10f503, u+10f508, u+10f51b, u+10f51d-10f51e, u+10f521-10f522, u+10f52b, u+10f530, u+10f535, u+10f53e, u+10f543-10f545, u+10f548-10f549, u+10f54e, u+10f555, u+10f559, u+10f55d, u+10f564, u+10f56c, u+10f56e-10f570, u+10f577-10f578, u+10f590, u+10f594-10f595, u+10f5a1-10f5a2, u+10f5aa-10f5ab, u+10f5b0, u+10f5b7, u+10f5ba, u+10f5bf, u+10f5ca, u+10f5db-10f5dc, u+10f5ef, u+10f5f2, u+10f5f6, u+10f5fb
}

@font-face {
    font-family: "Font Awesome 5 Duotone";
    font-display: block;
    font-weight: 900;
    src: url(../webfonts/pro-fa-duotone-900-3.woff2) format("woff2"), url(../webfonts/pro-fa-duotone-900-3.ttf) format("truetype");
    unicode-range: u+f069, u+f5fd, u+f621, u+f63b, u+f649-f64a, u+f64f, u+f653-f654, u+f656, u+f65b, u+f664, u+f673, u+f675, u+f67d, u+f67f, u+f695, u+f69c, u+f6a8, u+f6bf-f6c0, u+f6d5, u+f6e3, u+f6e9, u+f6f5, u+f6fa, u+f6ff-f700, u+f70b, u+f70e, u+f715, u+f71b, u+f72e-f72f, u+f733-f734, u+f747, u+f755, u+f757, u+f75c, u+f762, u+f773, u+f77c, u+f781, u+f784, u+f788, u+f7b2, u+f7b6, u+f7bd, u+f7d5, u+f7ee, u+f7ff, u+f801, u+f804, u+f813-f814, u+f82f-f830, u+f845-f846, u+f850, u+f855, u+f858-f859, u+f85c, u+f866, u+f86d, u+f871, u+f875, u+f893-f894, u+f897, u+f89f, u+f8a9, u+f8b1-f8b2, u+f8bb, u+f8c7, u+f8d6-f8d7, u+f8d9, u+f8df-f8e0, u+f8e7, u+f8ee-f8ef, u+f8fd, u+10f069, u+10f5fd, u+10f621, u+10f63b, u+10f649-10f64a, u+10f64f, u+10f653-10f654, u+10f656, u+10f65b, u+10f664, u+10f673, u+10f675, u+10f67d, u+10f67f, u+10f695, u+10f69c, u+10f6a8, u+10f6bf-10f6c0, u+10f6d5, u+10f6e3, u+10f6e9, u+10f6f5, u+10f6fa, u+10f6ff-10f700, u+10f70b, u+10f70e, u+10f715, u+10f71b, u+10f72e-10f72f, u+10f733-10f734, u+10f747, u+10f755, u+10f757, u+10f75c, u+10f762, u+10f773, u+10f77c, u+10f781, u+10f784, u+10f788, u+10f7b2, u+10f7b6, u+10f7bd, u+10f7d5, u+10f7ee, u+10f7ff, u+10f801, u+10f804, u+10f813-10f814, u+10f82f-10f830, u+10f845-10f846, u+10f850, u+10f855, u+10f858-10f859, u+10f85c, u+10f866, u+10f86d, u+10f871, u+10f875, u+10f893-10f894, u+10f897, u+10f89f, u+10f8a9, u+10f8b1-10f8b2, u+10f8bb, u+10f8c7, u+10f8d6-10f8d7, u+10f8d9, u+10f8df-10f8e0, u+10f8e7, u+10f8ee-10f8ef, u+10f8fd
}

@font-face {
    font-family: "Font Awesome 5 Duotone";
    font-display: block;
    font-weight: 900;
    src: url(../webfonts/pro-fa-duotone-900-4.woff2) format("woff2"), url(../webfonts/pro-fa-duotone-900-4.ttf) format("truetype");
    unicode-range: u+e000-e006, u+e008-e00f, u+e011-e012, u+e014-e016, u+e018-e019, u+e01c-e01d, u+e022-e023, u+e025-e02e, u+e030-e039, u+e03b-e041, u+e043-e044, u+e047, u+f1fa, u+f52c, u+f531, u+f536, u+f69f, u+10e000-10e006, u+10e008-10e00f, u+10e011-10e012, u+10e014-10e016, u+10e018-10e019, u+10e01c-10e01d, u+10e022-10e023, u+10e025-10e02e, u+10e030-10e039, u+10e03b-10e041, u+10e043-10e044, u+10e047, u+10f1fa, u+10f52c, u+10f531, u+10f536, u+10f69f
}

@font-face {
    font-family: "Font Awesome 5 Duotone";
    font-display: block;
    font-weight: 900;
    src: url(../webfonts/pro-fa-duotone-900-5.woff2) format("woff2"), url(../webfonts/pro-fa-duotone-900-5.ttf) format("truetype");
    unicode-range: u+e048, u+e04a-e051, u+e053-e054, u+e058-e05f, u+e061-e067, u+e069-e06d, u+e06f-e073, u+e075, u+e085-e086, u+10e048, u+10e04a-10e051, u+10e053-10e054, u+10e058-10e05f, u+10e061-10e067, u+10e069-10e06d, u+10e06f-10e073, u+10e075, u+10e085-10e086
}

@font-face {
    font-family: "Font Awesome 5 Duotone";
    font-display: block;
    font-weight: 900;
    src: url(../webfonts/pro-fa-duotone-900-9.woff2) format("woff2"), url(../webfonts/pro-fa-duotone-900-9.ttf) format("truetype");
    unicode-range: u+f80b, u+10f80b
}

@font-face {
    font-family: "Font Awesome 5 Duotone";
    font-display: block;
    font-weight: 900;
    src: url(../webfonts/pro-fa-duotone-900-12.woff2) format("woff2"), url(../webfonts/pro-fa-duotone-900-12.ttf) format("truetype");
    unicode-range: u+f8bc, u+10f8bc
}

@font-face {
    font-family: "Font Awesome 5 Duotone";
    font-display: block;
    font-weight: 900;
    src: url(../webfonts/pro-fa-duotone-900-13.woff2) format("woff2"), url(../webfonts/pro-fa-duotone-900-13.ttf) format("truetype");
    unicode-range: u+f000, u+f002, u+f009-f00b, u+f00d-f00e, u+f010, u+f013, u+f01e, u+f021-f022, u+f026-f029, u+f02b, u+f032-f039, u+f03b-f03c, u+f042-f044, u+f047-f049, u+f050-f05a, u+f05e, u+f066, u+f06a, u+f070-f071, u+f073-f074, u+f076, u+f079-f07a, u+10f000, u+10f002, u+10f009-10f00b, u+10f00d-10f00e, u+10f010, u+10f013, u+10f01e, u+10f021-10f022, u+10f026-10f029, u+10f02b, u+10f032-10f039, u+10f03b-10f03c, u+10f042-10f044, u+10f047-10f049, u+10f050-10f05a, u+10f05e, u+10f066, u+10f06a, u+10f070-10f071, u+10f073-10f074, u+10f076, u+10f079-10f07a
}

@font-face {
    font-family: "Font Awesome 5 Duotone";
    font-display: block;
    font-weight: 900;
    src: url(../webfonts/pro-fa-duotone-900-14.woff2) format("woff2"), url(../webfonts/pro-fa-duotone-900-14.ttf) format("truetype");
    unicode-range: u+f07c-f07e, u+f080, u+f083, u+f085, u+f089, u+f08b, u+f08d-f08e, u+f090, u+f094, u+f098, u+f09e, u+f0a0-f0a1, u+f0a4-f0a5, u+f0a7-f0ab, u+f0ae, u+f0b2, u+f0c3-f0c4, u+f0c7, u+f0ca-f0cb, u+f0cd, u+f0d0, u+f0d8-f0db, u+f0dd-f0de, u+f0e2-f0e3, u+f0e9-f0ea, u+f0ec-f0ee, u+f0f0-f0f2, u+f0f4, u+f0f9-f0fe, u+f100-f105, u+f107, u+f10a-f10b, u+f10d, u+f118-f11b, u+f120, u+f122, u+f124-f125, u+f127, u+f12b, u+f134, u+f137-f13a, u+f13e, u+f141-f144, u+f146, u+f148-f149, u+f381-f382, u+10f07c-10f07e, u+10f080, u+10f083, u+10f085, u+10f089, u+10f08b, u+10f08d-10f08e, u+10f090, u+10f094, u+10f098, u+10f09e, u+10f0a0-10f0a1, u+10f0a4-10f0a5, u+10f0a7-10f0ab, u+10f0ae, u+10f0b2, u+10f0c3-10f0c4, u+10f0c7, u+10f0ca-10f0cb, u+10f0cd, u+10f0d0, u+10f0d8-10f0db, u+10f0dd-10f0de, u+10f0e2-10f0e3, u+10f0e9-10f0ea, u+10f0ec-10f0ee, u+10f0f0-10f0f2, u+10f0f4, u+10f0f9-10f0fe, u+10f100-10f105, u+10f107, u+10f10a-10f10b, u+10f10d, u+10f118-10f11b, u+10f120, u+10f122, u+10f124-10f125, u+10f127, u+10f12b, u+10f134, u+10f137-10f13a, u+10f13e, u+10f141-10f144, u+10f146, u+10f148-10f149, u+10f381-10f382
}

@font-face {
    font-family: "Font Awesome 5 Duotone";
    font-display: block;
    font-weight: 900;
    src: url(../webfonts/pro-fa-duotone-900-15.woff2) format("woff2"), url(../webfonts/pro-fa-duotone-900-15.ttf) format("truetype");
    unicode-range: u+f14a-f14d, u+f150-f154, u+f156-f159, u+f15c-f15e, u+f160-f163, u+f165, u+f175-f178, u+f182-f183, u+f185, u+f187, u+f191-f193, u+f195, u+f197, u+f199, u+f19c-f19d, u+f1ac, u+f1b0, u+f1b3, u+f1b9-f1ba, u+f1c4-f1c9, u+f1cd, u+f1da, u+f1dd-f1de, u+f1e0-f1e3, u+f1f6, u+f1fb-f1fe, u+f200-f201, u+f204, u+f206-f207, u+f20b, u+f218, u+f21e, u+f221-f22c, u+f235-f236, u+f239, u+f240-f245, u+10f14a-10f14d, u+10f150-10f154, u+10f156-10f159, u+10f15c-10f15e, u+10f160-10f163, u+10f165, u+10f175-10f178, u+10f182-10f183, u+10f185, u+10f187, u+10f191-10f193, u+10f195, u+10f197, u+10f199, u+10f19c-10f19d, u+10f1ac, u+10f1b0, u+10f1b3, u+10f1b9-10f1ba, u+10f1c4-10f1c9, u+10f1cd, u+10f1da, u+10f1dd-10f1de, u+10f1e0-10f1e3, u+10f1f6, u+10f1fb-10f1fe, u+10f200-10f201, u+10f204, u+10f206-10f207, u+10f20b, u+10f218, u+10f21e, u+10f221-10f22c, u+10f235-10f236, u+10f239, u+10f240-10f245
}

@font-face {
    font-family: "Font Awesome 5 Duotone";
    font-display: block;
    font-weight: 900;
    src: url(../webfonts/pro-fa-duotone-900-16.woff2) format("woff2"), url(../webfonts/pro-fa-duotone-900-16.ttf) format("truetype");
    unicode-range: u+f247-f249, u+f24e, u+f252-f25c, u+f26c, u+f271-f274, u+f276-f277, u+f279-f27a, u+f28b, u+f28d, u+f290-f291, u+f29a, u+f29d, u+f2a1-f2a4, u+f2a8, u+f2b6, u+f2b9, u+f2bd, u+f2c1-f2c2, u+f2c7-f2cb, u+f2ce, u+f2d0-f2d1, u+f2d3, u+f2dc, u+f2e2-f2eb, u+f2ed-f2ee, u+f2f0-f2f6, u+f2f8-f2fb, u+f2fd-f2fe, u+f300-f301, u+f304-f30c, u+f4e6, u+f8e5, u+10f247-10f249, u+10f24e, u+10f252-10f25c, u+10f26c, u+10f271-10f274, u+10f276-10f277, u+10f279-10f27a, u+10f28b, u+10f28d, u+10f290-10f291, u+10f29a, u+10f29d, u+10f2a1-10f2a4, u+10f2a8, u+10f2b6, u+10f2b9, u+10f2bd, u+10f2c1-10f2c2, u+10f2c7-10f2cb, u+10f2ce, u+10f2d0-10f2d1, u+10f2d3, u+10f2dc, u+10f2e2-10f2eb, u+10f2ed-10f2ee, u+10f2f0-10f2f6, u+10f2f8-10f2fb, u+10f2fd-10f2fe, u+10f300-10f301, u+10f304-10f30c, u+10f4e6, u+10f8e5
}

@font-face {
    font-family: "Font Awesome 5 Duotone";
    font-display: block;
    font-weight: 900;
    src: url(../webfonts/pro-fa-duotone-900-17.woff2) format("woff2"), url(../webfonts/pro-fa-duotone-900-17.ttf) format("truetype");
    unicode-range: u+f30d-f315, u+f317-f319, u+f31c-f31e, u+f320-f327, u+f329-f32e, u+f330-f331, u+f333-f334, u+f336-f33e, u+f340-f34e, u+f350-f35b, u+f35d, u+f360-f362, u+f364-f367, u+f376-f377, u+f386-f387, u+f389-f38a, u+f39b-f39c, u+f3a0, u+f3a5, u+f3b3, u+f3be-f3bf, u+10f30d-10f315, u+10f317-10f319, u+10f31c-10f31e, u+10f320-10f327, u+10f329-10f32e, u+10f330-10f331, u+10f333-10f334, u+10f336-10f33e, u+10f340-10f34e, u+10f350-10f35b, u+10f35d, u+10f360-10f362, u+10f364-10f367, u+10f376-10f377, u+10f386-10f387, u+10f389-10f38a, u+10f39b-10f39c, u+10f3a0, u+10f3a5, u+10f3b3, u+10f3be-10f3bf
}

@font-face {
    font-family: "Font Awesome 5 Duotone";
    font-display: block;
    font-weight: 900;
    src: url(../webfonts/pro-fa-duotone-900-18.woff2) format("woff2"), url(../webfonts/pro-fa-duotone-900-18.ttf) format("truetype");
    unicode-range: u+f3c1-f3c2, u+f3c9, u+f3cd, u+f3cf, u+f3d1, u+f3dd-f3de, u+f3e0, u+f3ed, u+f3f0-f3f2, u+f3fa, u+f3fc, u+f3ff-f401, u+f406, u+f40f-f410, u+f422, u+f424, u+f432-f434, u+f436-f44a, u+f44c, u+f44e-f451, u+f453-f456, u+f458, u+f45a-f479, u+f4a1, u+10f3c1-10f3c2, u+10f3c9, u+10f3cd, u+10f3cf, u+10f3d1, u+10f3dd-10f3de, u+10f3e0, u+10f3ed, u+10f3f0-10f3f2, u+10f3fa, u+10f3fc, u+10f3ff-10f401, u+10f406, u+10f40f-10f410, u+10f422, u+10f424, u+10f432-10f434, u+10f436-10f44a, u+10f44c, u+10f44e-10f451, u+10f453-10f456, u+10f458, u+10f45a-10f479, u+10f4a1
}

@font-face {
    font-family: "Font Awesome 5 Duotone";
    font-display: block;
    font-weight: 900;
    src: url(../webfonts/pro-fa-duotone-900-19.woff2) format("woff2"), url(../webfonts/pro-fa-duotone-900-19.ttf) format("truetype");
    unicode-range: u+f47a-f47c, u+f47e-f480, u+f482-f489, u+f48b-f48e, u+f491-f492, u+f495-f497, u+f499-f4a0, u+f4a2-f4b6, u+f4b8-f4c9, u+f4cb, u+f4cd-f4d0, u+f4d2-f4d4, u+f4d6, u+f4d9-f4e1, u+f4e3, u+10f47a-10f47c, u+10f47e-10f480, u+10f482-10f489, u+10f48b-10f48e, u+10f491-10f492, u+10f495-10f497, u+10f499-10f4a0, u+10f4a2-10f4b6, u+10f4b8-10f4c9, u+10f4cb, u+10f4cd-10f4d0, u+10f4d2-10f4d4, u+10f4d6, u+10f4d9-10f4e1, u+10f4e3
}

@font-face {
    font-family: "Font Awesome 5 Duotone";
    font-display: block;
    font-weight: 900;
    src: url(../webfonts/pro-fa-duotone-900-20.woff2) format("woff2"), url(../webfonts/pro-fa-duotone-900-20.ttf) format("truetype");
    unicode-range: u+f4fa-f502, u+f504-f507, u+f509, u+f515-f51a, u+f51c, u+f51f-f520, u+f523-f52a, u+f52d-f52f, u+f532-f534, u+f537-f53d, u+f53f-f540, u+f542, u+f546-f547, u+f54a-f54d, u+f54f-f554, u+f556-f558, u+f55a-f55c, u+f55e-f563, u+f565-f56b, u+f56d, u+f571-f576, u+f579-f57d, u+10f4fa-10f502, u+10f504-10f507, u+10f509, u+10f515-10f51a, u+10f51c, u+10f51f-10f520, u+10f523-10f52a, u+10f52d-10f52f, u+10f532-10f534, u+10f537-10f53d, u+10f53f-10f540, u+10f542, u+10f546-10f547, u+10f54a-10f54d, u+10f54f-10f554, u+10f556-10f558, u+10f55a-10f55c, u+10f55e-10f563, u+10f565-10f56b, u+10f56d, u+10f571-10f576, u+10f579-10f57d
}

@font-face {
    font-family: "Font Awesome 5 Duotone";
    font-display: block;
    font-weight: 900;
    src: url(../webfonts/pro-fa-duotone-900-21.woff2) format("woff2"), url(../webfonts/pro-fa-duotone-900-21.ttf) format("truetype");
    unicode-range: u+f57e-f58f, u+f591, u+f593, u+f596-f59d, u+f59f-f5a0, u+f5a4-f5a7, u+f5a9, u+f5ac-f5af, u+f5b1, u+f5b3-f5b4, u+f5b6, u+f5b8-f5b9, u+f5bb-f5bd, u+f5c0-f5c5, u+f5c7-f5c9, u+f5cb, u+f5cd-f5ce, u+f5d0-f5da, u+f5dd-f5ee, u+f5f0, u+10f57e-10f58f, u+10f591, u+10f593, u+10f596-10f59d, u+10f59f-10f5a0, u+10f5a4-10f5a7, u+10f5a9, u+10f5ac-10f5af, u+10f5b1, u+10f5b3-10f5b4, u+10f5b6, u+10f5b8-10f5b9, u+10f5bb-10f5bd, u+10f5c0-10f5c5, u+10f5c7-10f5c9, u+10f5cb, u+10f5cd-10f5ce, u+10f5d0-10f5da, u+10f5dd-10f5ee, u+10f5f0
}

@font-face {
    font-family: "Font Awesome 5 Duotone";
    font-display: block;
    font-weight: 900;
    src: url(../webfonts/pro-fa-duotone-900-22.woff2) format("woff2"), url(../webfonts/pro-fa-duotone-900-22.ttf) format("truetype");
    unicode-range: u+f0e4, u+f3fd, u+f5f3-f5f5, u+f5f8-f5f9, u+f5fc, u+f5fe-f60e, u+f610-f611, u+f613-f620, u+f622-f63a, u+f63c-f63e, u+f640-f641, u+f643-f648, u+f64b-f64e, u+f650-f652, u+f655, u+f657-f65a, u+f65c, u+10f0e4, u+10f3fd, u+10f5f3-10f5f5, u+10f5f8-10f5f9, u+10f5fc, u+10f5fe-10f60e, u+10f610-10f611, u+10f613-10f620, u+10f622-10f63a, u+10f63c-10f63e, u+10f640-10f641, u+10f643-10f648, u+10f64b-10f64e, u+10f650-10f652, u+10f655, u+10f657-10f65a, u+10f65c
}

@font-face {
    font-family: "Font Awesome 5 Duotone";
    font-display: block;
    font-weight: 900;
    src: url(../webfonts/pro-fa-duotone-900-23.woff2) format("woff2"), url(../webfonts/pro-fa-duotone-900-23.ttf) format("truetype");
    unicode-range: u+f65d-f663, u+f665-f672, u+f674, u+f676-f67c, u+f67e, u+f680-f694, u+f696-f69b, u+f69e, u+f6a0-f6a7, u+f6a9-f6ae, u+f6b0-f6be, u+f6c1-f6c3, u+10f65d-10f663, u+10f665-10f672, u+10f674, u+10f676-10f67c, u+10f67e, u+10f680-10f694, u+10f696-10f69b, u+10f69e, u+10f6a0-10f6a7, u+10f6a9-10f6ae, u+10f6b0-10f6be, u+10f6c1-10f6c3
}

@font-face {
    font-family: "Font Awesome 5 Duotone";
    font-display: block;
    font-weight: 900;
    src: url(../webfonts/pro-fa-duotone-900-24.woff2) format("woff2"), url(../webfonts/pro-fa-duotone-900-24.ttf) format("truetype");
    unicode-range: u+f6c4-f6c8, u+f6cb, u+f6cd-f6d4, u+f6d6-f6db, u+f6dd-f6e2, u+f6e4-f6e8, u+f6ea-f6f4, u+f6f6-f6f9, u+f6fb-f6fe, u+f701-f703, u+f705-f70a, u+f70c-f70d, u+f70f-f714, u+f716-f71a, u+f71c-f72d, u+10f6c4-10f6c8, u+10f6cb, u+10f6cd-10f6d4, u+10f6d6-10f6db, u+10f6dd-10f6e2, u+10f6e4-10f6e8, u+10f6ea-10f6f4, u+10f6f6-10f6f9, u+10f6fb-10f6fe, u+10f701-10f703, u+10f705-10f70a, u+10f70c-10f70d, u+10f70f-10f714, u+10f716-10f71a, u+10f71c-10f72d
}

@font-face {
    font-family: "Font Awesome 5 Duotone";
    font-display: block;
    font-weight: 900;
    src: url(../webfonts/pro-fa-duotone-900-25.woff2) format("woff2"), url(../webfonts/pro-fa-duotone-900-25.ttf) format("truetype");
    unicode-range: u+f732, u+f735-f746, u+f748-f754, u+f756, u+f758-f75b, u+f75e-f761, u+f763-f772, u+f774-f777, u+f779, u+f77d-f780, u+f782-f783, u+f786-f787, u+f78a-f78c, u+f78e-f78f, u+f792-f796, u+f79a-f7a3, u+10f732, u+10f735-10f746, u+10f748-10f754, u+10f756, u+10f758-10f75b, u+10f75e-10f761, u+10f763-10f772, u+10f774-10f777, u+10f779, u+10f77d-10f780, u+10f782-10f783, u+10f786-10f787, u+10f78a-10f78c, u+10f78e-10f78f, u+10f792-10f796, u+10f79a-10f7a3
}

@font-face {
    font-family: "Font Awesome 5 Duotone";
    font-display: block;
    font-weight: 900;
    src: url(../webfonts/pro-fa-duotone-900-26.woff2) format("woff2"), url(../webfonts/pro-fa-duotone-900-26.ttf) format("truetype");
    unicode-range: u+f7a4-f7ae, u+f7b4-f7b5, u+f7b7-f7ba, u+f7be-f7c5, u+f7c7-f7d2, u+f7d4, u+f7d7-f7de, u+f7e2, u+f7e4-f7ed, u+f7ef-f7fe, u+f800, u+f802-f803, u+f805-f809, u+f80d-f812, u+f815-f817, u+10f7a4-10f7ae, u+10f7b4-10f7b5, u+10f7b7-10f7ba, u+10f7be-10f7c5, u+10f7c7-10f7d2, u+10f7d4, u+10f7d7-10f7de, u+10f7e2, u+10f7e4-10f7ed, u+10f7ef-10f7fe, u+10f800, u+10f802-10f803, u+10f805-10f809, u+10f80d-10f812, u+10f815-10f817
}

@font-face {
    font-family: "Font Awesome 5 Duotone";
    font-display: block;
    font-weight: 900;
    src: url(../webfonts/pro-fa-duotone-900-27.woff2) format("woff2"), url(../webfonts/pro-fa-duotone-900-27.ttf) format("truetype");
    unicode-range: u+f818-f82e, u+f831-f833, u+f83e, u+f843-f844, u+f847-f84f, u+f851-f854, u+f856-f857, u+f85a-f85b, u+f85d-f865, u+f867-f86c, u+f86e-f870, u+f872-f874, u+f876-f88c, u+10f818-10f82e, u+10f831-10f833, u+10f83e, u+10f843-10f844, u+10f847-10f84f, u+10f851-10f854, u+10f856-10f857, u+10f85a-10f85b, u+10f85d-10f865, u+10f867-10f86c, u+10f86e-10f870, u+10f872-10f874, u+10f876-10f88c
}

@font-face {
    font-family: "Font Awesome 5 Duotone";
    font-display: block;
    font-weight: 900;
    src: url(../webfonts/pro-fa-duotone-900-28.woff2) format("woff2"), url(../webfonts/pro-fa-duotone-900-28.ttf) format("truetype");
    unicode-range: u+f88d-f892, u+f895-f896, u+f898-f89d, u+f8a0-f8a5, u+f8a7-f8a8, u+f8aa-f8b0, u+f8b3-f8ba, u+f8bd-f8c6, u+f8c8-f8c9, u+f8cb-f8d1, u+f8d3-f8d5, u+f8d8, u+f8da-f8de, u+f8e2-f8e4, u+f8e6, u+f8e9-f8ed, u+f8f0-f8fc, u+f8fe-f8ff, u+10f88d-10f892, u+10f895-10f896, u+10f898-10f89d, u+10f8a0-10f8a5, u+10f8a7-10f8a8, u+10f8aa-10f8b0, u+10f8b3-10f8ba, u+10f8bd-10f8c6, u+10f8c8-10f8c9, u+10f8cb-10f8d1, u+10f8d3-10f8d5, u+10f8d8, u+10f8da-10f8de, u+10f8e2-10f8e4, u+10f8e6, u+10f8e9-10f8ed, u+10f8f0-10f8fc, u+10f8fe-10f8ff
}