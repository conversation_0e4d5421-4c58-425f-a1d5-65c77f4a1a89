@media (prefers-reduced-motion:no-preference) {
    :root {
        scroll-behavior: smooth
    }
}

body {
    -webkit-text-size-adjust: 100%;
    color: var(--bs-gray-800);
    line-height: 1.7;
    overflow-x: hidden;
    position: relative;
    scroll-behavior: smooth !important
}

::-webkit-scrollbar {
    background: hsla(0, 0%, 100%, .445);
    border-radius: 0 !important;
    height: 5px !important;
    width: 5px !important
}

::-webkit-scrollbar-thumb {
    background-color: #d4d4d4 !important
}

::-webkit-scrollbar-thumb:hover {
    background-color: #5c258b !important
}

.img {
    max-width: 100%;
    transition: all .3s ease-out 0s
}

.ratio-1x1 {
    aspect-ratio: 1/1 !important
}

.bg-gray-100 {
    background-color: var(--bs-gray-100) !important
}

.bg-gray-200 {
    background-color: var(--bs-gray-200) !important
}

.bg-gray-300 {
    background-color: var(--bs-gray-300) !important
}

.bg-gray-400 {
    background-color: var(--bs-gray-400) !important
}

.bg-gray-500 {
    background-color: var(--bs-gray-500) !important
}

.bg-gray-600 {
    background-color: var(--bs-gray-600) !important
}

.border-gray-300 {
    --bs-border-color: var(--bs-gray-300) !important;
    border-color: var(--bs-gray-300) !important
}

.bg-none {
    background: none !important
}

.fs-sm {
    font-size: .85rem !important
}

.button,
a {
    transition: all .3s ease-out 0s
}

.btn:focus,
.button:focus,
a:focus {
    box-shadow: none;
    outline: none;
    text-decoration: none
}

a:hover {
    text-decoration: none
}

button:focus,
input:focus,
textarea,
textarea:focus {
    outline: 0
}

.uppercase {
    text-transform: uppercase
}

.capitalize {
    text-transform: capitalize
}

a {
    text-decoration: none !important
}

ul {
    margin: 0;
    padding: 0
}

li {
    list-style: none
}

::-moz-selection {
    background: #d6b161;
    background: #444;
    color: #fff;
    text-shadow: none
}

::selection {
    background: #444;
    color: #fff;
    text-shadow: none
}

::-moz-placeholder {
    color: #555;
    font-size: 14px;
    opacity: 1
}

::placeholder {
    color: #555;
    font-size: 14px;
    opacity: 1
}

.breadcrumb>.active {
    color: #888
}

.df-header {
    padding-block: 1.15rem
}

.df-header .input-group input {
    border-color: var(--bs-primary);
    border-width: 1px !important
}

.df-header .input-group input::-moz-placeholder {
    color: var(--bs-gray-400) !important
}

.df-header .input-group input::placeholder {
    color: var(--bs-gray-400) !important
}

.scroll-top {
    border: none;
    border-radius: 50%;
    bottom: 105%;
    color: #fff;
    cursor: pointer;
    font-size: 16px;
    height: 50px;
    line-height: 50px;
    opacity: 0;
    position: fixed;
    right: 50px;
    text-align: center;
    transition: 1s ease;
    width: 50px;
    z-index: 9
}

.scroll-top.open {
    bottom: 62px;
    opacity: 1
}

.scroll-top:hover {
    background: #222
}

.product-card {
    border: none !important
}

.product-card .product-card__img {
    aspect-ratio: 800/800 !important;
    background: var(--bs-gray-100);
    overflow: hidden
}

.product-card .card-body.bg-gray-200 {
    --bs-gray-200: var(--bs-gray-100) !important
}

.product-card,
.product-card img {
    transition: all .5s ease
}

.product-card:hover {
    img {
        scale: 1.05
    }
}

.slider {
    display: flex;
    flex-wrap: nowrap !important;
    transition: transform .3s ease-in-out
}

.header-top-area {
    background: #eee;
    padding: 14px 0
}

.checkout-form .form-grp .custom-select,
.shop-meta-right form .custom-select {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background: url(../img/icons/nw_selarw.png) no-repeat scroll 97.5%;
    background-color: #fff;
    border: 1.5px solid #dfdfdf;
    border-left: none;
    border-radius: 0;
    border-right: none;
    box-shadow: none;
    color: #818181;
    display: inline-block;
    font-size: 12px;
    font-weight: 400;
    height: 47px;
    line-height: 1.6;
    padding: 10px 42px 10px 16px;
    transition: .3s ease-in-out;
    vertical-align: middle;
    width: 170px
}

.navbar-wrap {
    display: flex;
    flex-grow: 1
}

.navbar-wrap ul {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    margin: auto
}

.navbar-wrap ul li,
.navbar-wrap ul li a {
    display: block;
    position: relative
}

.navbar-wrap ul li a {
    color: #000;
    font-size: 14px;
    font-weight: 500;
    line-height: 1;
    padding: 32px 20px;
    text-transform: uppercase;
    z-index: 1
}

.navbar-wrap>ul>li>a:before {
    border-radius: 3px 3px 0 0;
    bottom: 0;
    content: "";
    height: 4px;
    left: 0;
    margin: 0 auto;
    opacity: 0;
    position: absolute;
    right: 0;
    transition: all .3s ease-out 0s;
    width: 80%
}

.navbar-wrap>ul>li:last-child>a:before {
    margin-left: auto;
    margin-right: 0
}

.navbar-wrap>ul>li:last-child a {
    padding-right: 0
}

.navbar-wrap>ul>li.active>a,
.navbar-wrap>ul>li:hover>a {
    color: #fff
}

.navbar-wrap>ul>li.active>a:before,
.navbar-wrap>ul>li:hover>a:before {
    opacity: 1
}

.main-menu .navigation li.dropdown .dropdown-btn {
    display: none
}

.header-action>ul {
    align-items: center;
    display: flex;
    margin-left: 25px
}

.header-action>ul li {
    margin-left: 25px;
    position: relative
}

.header-action ul li:first-child {
    margin-left: 0
}

.header-action ul li a {
    color: #2d2d2d;
    font-size: 20px
}

.minicart .cart-img img {
    width: 100%
}

.cart-content h4 {
    line-height: 1.1
}

.minicart .cart-content h4 a {
    background: none;
    color: #2d2d2d;
    font-size: 15px;
    font-weight: 500
}

.minicart .cart-price span {
    color: #616161;
    font-size: 13px;
    font-weight: 400;
    margin-left: 6px
}

.minicart .cart-price .new {
    font-size: 14px;
    margin-left: 0
}

.minicart .del-icon>a {
    font-size: 18px
}

.total-price {
    border-top: 1px solid #473151;
    margin-top: 10px;
    overflow: hidden;
    padding-top: 25px
}

.total-price span {
    color: #616161;
    font-weight: 500
}

.navbar-wrap ul li .submenu {
    background-color: #fff;
    background: #fff;
    border: 1px solid #f5f5f5;
    border-radius: 0;
    box-shadow: 0 13px 25px -12px rgba(0, 0, 0, .25);
    box-shadow: 0 30px 70px 0 hsla(216, 2%, 55%, .15);
    display: block;
    left: 0;
    margin: 0;
    min-width: 230px;
    opacity: 0;
    padding: 18px 0;
    position: absolute;
    right: 0;
    top: 100%;
    transform: scaleY(0);
    transform-origin: 0 0;
    transition: all .3s ease-in-out;
    visibility: hidden;
    z-index: 9
}

.navbar-wrap ul li .submenu li {
    display: block;
    margin-left: 0;
    text-align: left
}

.navbar-wrap ul li .submenu li a {
    color: #252525;
    font-weight: 400;
    line-height: 40px;
    padding: 0 10px 0 25px;
    text-transform: capitalize;
    transition: all .3s ease-in-out
}

.navbar-wrap ul li:hover>.submenu {
    opacity: 1;
    transform: scale(1);
    visibility: visible
}

.sticky-menu {
    animation: fadeInDown .2s ease-in-out 0s 1 normal none running;
    background: #fff;
    border-radius: 0;
    left: 0;
    margin: auto;
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 99
}

.sticky-menu .navbar-wrap ul li a {
    color: #252525
}

.category-menu {
    background: #fff;
    border: 1px solid #ebebeb;
    box-shadow: 0 1px 16px 0 hsla(0, 0%, 86%, .22);
    display: none;
    left: 0;
    padding: 15px 0;
    position: absolute;
    top: 100%;
    width: 100%;
    z-index: 2
}

.category-menu li {
    display: block
}

.category-menu>li>a,
.more_slide_open>li>a {
    background: transparent;
    border: 1px solid transparent;
    color: #6e6e6e;
    display: block;
    font-size: 13px;
    padding: 9px 50px 9px 20px;
    position: relative
}

.category-menu>li:hover>a {
    background: #f2f1f1;
    border-color: #ebebeb
}

.sticky-menu.menu-area .mobile-nav-toggler {
    color: #252525
}

.custom-container-two {
    margin-left: auto;
    margin-right: auto;
    max-width: 1428px;
    padding-inline: 1rem;
    width: 100%
}

.more_slide_open>li>a {
    border: none;
    padding: 7px 20px
}

.more_slide_open {
    display: none
}

.category-menu .more_categories {
    align-items: center;
    cursor: pointer;
    display: flex;
    font-weight: 700;
    justify-content: space-between;
    margin-top: 10px;
    padding: 10px 20px
}

.mobile-menu {
    height: 100%;
    max-width: 100%;
    opacity: 0;
    padding-right: 30px;
    position: fixed;
    right: 0;
    top: 0;
    visibility: hidden;
    width: 300px;
    z-index: 99
}

.mobile-menu-visible {
    overflow: hidden
}

.mobile-menu-visible .mobile-menu {
    opacity: 1;
    visibility: visible
}

.mobile-menu .navigation li.current>a:before {
    height: 100%
}

.mobile-menu .menu-backdrop {
    background: #000;
    height: 100%;
    position: fixed;
    right: 0;
    top: 0;
    transform: translateX(101%);
    transition: all .9s ease;
    -moz-transition: all .9s ease;
    -webkit-transition: all .9s ease;
    -ms-transition: all .9s ease;
    -o-transition: all .9s ease;
    width: 100%;
    z-index: 1
}

.mobile-menu-visible .mobile-menu .menu-backdrop {
    opacity: .7;
    transform: translateX(0);
    transition: all .7s ease;
    visibility: visible
}

.mobile-menu .menu-box {
    background: #0465d2;
    border-radius: 0;
    height: 100%;
    left: 0;
    max-height: 100%;
    opacity: 0;
    overflow-x: hidden;
    overflow-y: auto;
    padding: 0;
    position: absolute;
    top: 0;
    transform: translateX(101%);
    visibility: hidden;
    width: 100%;
    z-index: 5
}

.mobile-menu-visible .mobile-menu .menu-box {
    opacity: 1;
    transform: translateX(0);
    transition: all .7s ease;
    visibility: visible
}

.mobile-menu .close-btn {
    color: #fff;
    cursor: pointer;
    font-size: 30px;
    line-height: 30px;
    position: absolute;
    right: 30px;
    text-align: center;
    top: 10px;
    transition: all .9s ease;
    width: 24px;
    z-index: 10
}

.close {
    background: none !important;
    border: none !important;
    box-shadow: 0 0 0 transparent !important;
    cursor: pointer
}

.mobile-menu-visible .mobile-menu .close-btn {
    transform: rotate(1turn)
}

.mobile-menu .navigation {
    display: block;
    float: none;
    position: relative;
    width: 100%
}

.mobile-menu .navigation li {
    border-top: 1px solid hsla(0, 0%, 100%, .1);
    display: block;
    position: relative
}

.mobile-menu .navigation:last-child {
    border-bottom: 1px solid hsla(0, 0%, 100%, .1)
}

.mobile-menu .navigation li>ul>li:first-child {
    border-top: 1px solid hsla(0, 0%, 100%, .1)
}

.mobile-menu .navigation li>a {
    border: none;
    color: #fff;
    display: block;
    font-size: 13px;
    font-weight: 400;
    line-height: 24px;
    padding: 10px 25px;
    position: relative;
    text-transform: uppercase;
    transition: all .5s ease
}

.mobile-menu .navigation li ul li>a {
    font-size: 15px;
    font-weight: 400;
    margin-left: 20px;
    text-transform: capitalize
}

.mobile-menu .navigation li>a:before {
    content: "";
    height: 0;
    left: 0;
    position: absolute;
    top: 0;
    transition: all .5s ease
}

.mobile-menu .navigation li.dropdown .dropdown-btn {
    background: transparent !important;
    border-radius: 2px;
    color: #fff;
    cursor: pointer;
    font-size: 16px;
    height: 32px;
    line-height: 32px;
    position: absolute;
    right: 6px;
    text-align: center;
    top: 6px;
    transition: all .5s ease;
    width: 32px;
    z-index: 5
}

.mobile-menu .navigation li.dropdown .dropdown-btn.open {
    transform: rotate(90deg)
}

.mobile-menu .navigation li>ul,
.mobile-menu .navigation li>ul>li>ul {
    display: none
}

.mobile-menu .social-links {
    padding: 30px 25px;
    position: relative;
    text-align: center
}

.mobile-menu .social-links li {
    display: inline-block;
    margin: 0 10px 10px;
    position: relative
}

.mobile-menu .social-links li a {
    color: #fff;
    font-size: 16px;
    line-height: 32px;
    position: relative;
    transition: all .5s ease
}

.menu-area .mobile-nav-toggler {
    color: #fff;
    cursor: pointer;
    display: none;
    float: right;
    font-size: 20px;
    line-height: 1.5;
    margin-left: 18px;
    margin-top: 9px;
    position: relative
}

.breadcrumb-bg {
    background-position: 50%;
    background-size: cover;
    padding-bottom: 85px;
    padding-top: 100px
}

.breadcrumb-content h2 {
    font-size: 45px;
    line-height: 1;
    margin-bottom: 15px;
    text-transform: uppercase
}

.breadcrumb-content .breadcrumb {
    border-radius: 50px;
    display: inline-block;
    list-style: none;
    margin-bottom: 0;
    padding: 10px 40px
}

.breadcrumb-content .breadcrumb li,
.breadcrumb-content .breadcrumb li a {
    color: #6d6c6c;
    display: inline-block;
    padding: 0;
    text-decoration: none
}

.svg-social {
    fill: #fff;
    height: 23px;
    width: 23px
}

.coming-time {
    align-items: center;
    display: flex
}

.time-count {
    background: #252525;
    border-radius: 3px;
    color: #fff;
    font-size: 12px;
    font-weight: 500;
    line-height: 1;
    margin-left: 6px;
    padding: 8px 12px;
    text-align: center;
    text-transform: uppercase;
    width: 50px
}

.time-count span {
    display: block;
    font-size: 19px;
    font-weight: 700;
    line-height: 1;
    margin-bottom: 2px
}

.time-count.sec,
.viewed-item-bottom .progress-bar {
    background: #ffa800
}

.core-features-item {
    position: relative;
    text-align: center
}

.core-features-icon {
    align-items: flex-end;
    color: #949494;
    display: flex;
    font-size: 38px;
    justify-content: center;
    margin-bottom: 20px;
    min-height: 47px
}

.core-features-icon img {
    transform: rotateY(0);
    transition: .5s linear
}

.core-features-item:hover .core-features-icon img {
    transform: rotateY(1turn)
}

.core-features-content h6 {
    color: #414141;
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 8px;
    transition: all .3s ease-out 0s
}

.warranty-icon {
    align-items: center;
    color: var(--bs-primary);
    display: flex;
    font-size: 3rem;
    justify-content: center
}

.core-features-content span {
    display: block;
    font-size: 14px;
    font-weight: 500
}

.core-features-border {
    padding-bottom: 0;
    padding-top: 20px
}

.core-features-item:after {
    border-radius: 50%;
    border-right: 1px solid #f2f2f2;
    content: "";
    height: 115px;
    position: absolute;
    right: -15px;
    top: 50%;
    transform: translateY(-50%);
    width: 1px
}

.core-features-border .row [class*=col-]:last-child .core-features-item:after {
    display: none
}

.section-title {
    position: relative
}

.limited-offer-title .sub-title,
.section-title .sub-title {
    color: #111827;
    display: block;
    font-size: 12px;
    font-weight: 500;
    line-height: 1;
    margin-bottom: 15px;
    text-transform: uppercase
}

.limited-offer-title .title,
.section-title .title {
    font-size: 31px;
    letter-spacing: 1px;
    margin-bottom: 0;
    text-transform: uppercase
}

@keyframes shine {
    to {
        left: 125%
    }
}

.product-menu {
    align-items: center;
    background: #f7f7f5;
    border-radius: 30px;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    padding: 20px 20px 10px
}

.product-menu button {
    background: none;
    border: none;
    color: #6d6b6b;
    cursor: pointer;
    font-size: 13px;
    font-weight: 700;
    margin: 0 15px 7px;
    padding: 0;
    text-transform: uppercase;
    transition: all .3s ease-out 0s
}

.exclusive-item-three .rating i,
.sidebar-product-content .rating i {
    color: #ffa800;
    font-size: 12px
}

.exclusive-item-three .rating i:last-child,
.sidebar-product-content .rating i:last-child {
    color: #e8ebf2
}

.viewed-item-bottom {
    padding: 25px 25px 0
}

.viewed-item-bottom ul {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between
}

.viewed-item-bottom ul li {
    color: #909090;
    display: block;
    font-size: 12px;
    font-weight: 500;
    line-height: 1;
    margin-bottom: 12px
}

.viewed-item-bottom .progress {
    background-color: #ebebeb;
    border-radius: 3px;
    display: flex;
    font-size: .75rem;
    height: 6px;
    overflow: hidden
}

.viewed-item-bottom .progress-bar {
    border-radius: 3px
}

.popular-active .slick-next {
    left: auto;
    right: -100px
}

.exclusive-item-three .action {
    align-items: center;
    background: #fff;
    bottom: 20px;
    box-shadow: 0 4px 21px 0 hsla(0, 1%, 45%, .19);
    display: flex;
    justify-content: center;
    left: 0;
    opacity: 0;
    padding: 15px 20px;
    position: absolute;
    right: 0;
    transition: all .3s ease-out 0s
}

.exclusive-item-three:hover .action {
    bottom: 0;
    opacity: 1
}

.exclusive-item-three .action li {
    margin: 0 7.5px
}

.exclusive-item-three .action li a {
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 7px 0 hsla(0, 1%, 45%, .18);
    color: #2b2b2b;
    display: block;
    font-size: 20px;
    height: 42px;
    line-height: 42px;
    width: 56px
}

.exclusive-item-three .action li a:hover {
    background: #030712;
    color: #fff
}

.exclusive-item-three {
    margin-left: 25px
}

.best-cat-border {
    border-bottom: 1px solid #e3e3e3
}

img {
    max-width: 100% !important
}

.blog-post-content h4 {
    font-size: 22px;
    margin-bottom: 7px
}

.blog-post-meta ul {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    margin-bottom: 15px
}

.blog-post-meta ul li {
    color: #a3a3a3;
    display: block;
    font-size: 12px;
    font-weight: 500;
    letter-spacing: 1px;
    margin-right: 20px;
    text-transform: uppercase
}

.blog-post-meta ul li:last-child {
    margin-right: 0
}

.blog-post-meta ul li i {
    margin-right: 5px
}

.blog-post-meta ul li a {
    color: #a3a3a3;
    font-size: 12px;
    font-weight: 500;
    letter-spacing: 1px;
    text-transform: uppercase
}

.blog-post-content p {
    margin-bottom: 20px;
    padding: 0 20px
}

.blog-thumb img {
    border-radius: 5px;
    width: 100%
}

.s-blog-post-bottom {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between
}

.blog-sidebar {
    margin-left: 25px
}

.sidebar-search-form {
    position: relative
}

.sidebar-search-form button {
    background: transparent;
    border: none;
    color: #484848;
    line-height: 1;
    padding: 10px;
    position: absolute;
    top: 50%;
    transform: translateY(-50%)
}

.blog-sidebar-title h5 {
    font-size: 16px;
    margin-bottom: 0;
    text-transform: uppercase
}

.sidebar-newsletter {
    position: relative
}

.sidebar-newsletter input {
    border: none;
    border-bottom: 1px dashed #dadada;
    font-size: 14px;
    padding: 0 0 10px;
    width: 100%
}

.sidebar-newsletter button {
    background: transparent;
    border: none;
    color: #393939;
    font-size: 16px;
    line-height: 1;
    padding: 0 0 10px;
    position: absolute;
    top: 0
}

.blog-sidebar-tag ul {
    align-items: center;
    display: flex;
    flex-wrap: wrap
}

.blog-sidebar-tag ul li {
    margin-right: 8px;
    margin-top: 8px
}

.blog-details-wrap .blog-post-meta ul {
    justify-content: flex-start;
    margin-bottom: 3px
}

.blog-bottom-meta ul li i,
.blog-details-wrap .blog-post-meta ul li,
.blog-details-wrap .blog-post-meta ul li i {
    color: #030712
}

.blog-details-wrap .blog-post-content p {
    margin-bottom: 15px;
    padding: 0
}

.blog-details-wrap blockquote {
    align-items: center;
    color: #252525;
    display: flex;
    font-size: 15px;
    font-weight: 500;
    line-height: 1.9;
    margin: 30px 0;
    text-transform: uppercase
}

.blog-bottom-meta ul {
    align-items: center;
    display: flex;
    flex-wrap: wrap
}

.blog-bottom-meta ul li {
    margin-right: 20px
}

.blog-bottom-meta ul li:last-child {
    margin-right: 0
}

.blog-bottom-meta ul li i {
    margin-right: 5px
}

.blog-bottom-meta ul li,
.blog-bottom-meta ul li a {
    color: #868686
}

.blog-details-wrap {
    border: none;
    margin-bottom: 50px;
    padding-bottom: 0
}

.blog-details-wrap .s-blog-post-bottom {
    border-top: 1px dashed #dadada;
    margin-top: 50px;
    padding-top: 25px
}

.b-details-inner-title {
    font-size: 20px;
    text-transform: uppercase
}

.blog-comment ul li {
    border-bottom: 1px dashed #dadada;
    display: block;
    margin-bottom: 10px;
    overflow: hidden;
    padding-bottom: 10px
}

.blog-comment ul li .single-comment {
    align-items: center;
    display: flex
}

.comment-avatar-img {
    margin-right: 25px
}

.comment-avatar-info {
    margin-bottom: 10px;
    overflow: hidden
}

.comment-avatar-info h5 {
    display: inline-block;
    font-size: 18px;
    margin-bottom: 0
}

.comment-avatar-info h5 span {
    color: #7d7d7d;
    font-size: 12px;
    font-weight: 500;
    margin-left: 10px
}

.comment-text p {
    margin-bottom: 0;
    padding-right: 100px
}

.blog-comment ul li:last-child {
    margin-bottom: 0
}

.comment-form textarea {
    height: 160px;
    padding: 20px
}

.comment-form input,
.comment-form textarea {
    background: #f2f0ed;
    border: none;
    color: #6c6c6c;
    font-size: 14px;
    margin-bottom: 20px;
    transition: .3s;
    width: 100%
}

.comment-form input {
    padding: 15px 20px
}

.comment-form input::-moz-placeholder,
.comment-form textarea::-moz-placeholder {
    color: #a7a7a7
}

.comment-form input::placeholder,
.comment-form textarea::placeholder {
    color: #a7a7a7
}

.comment-check-box input {
    margin: 5px 10px 0 0;
    width: auto
}

.comment-check-box label {
    color: #7d7d7d;
    font-size: 14px;
    margin: 0;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.comment-check-box {
    align-items: flex-start;
    display: flex;
    margin-bottom: 25px;
    margin-top: 5px
}

.wishlist-area .table thead th {
    background: #000;
    border-bottom: 1px solid #b6b6b652;
    border-top: 1px solid #b6b6b652;
    color: #fff;
    font-size: 13px;
    font-weight: 500;
    padding: 15px;
    text-transform: uppercase;
    vertical-align: bottom
}

.wishlist-area .table td,
.wishlist-area .table th {
    border-bottom: 1px solid #b6b6b652;
    border-top: none;
    padding: 25px 15px;
    vertical-align: middle
}

.wishlist-area tbody .product-thumbnail a.wishlist-remove {
    color: #7f7e7e;
    font-size: 16px;
    margin: 0 8px
}

.wishlist-area tbody .product-name h4 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 5px
}

.wishlist-area tbody .product-name p {
    line-height: 26px;
    margin-bottom: 10px
}

.wishlist-area tbody .product-name span {
    color: #666;
    font-size: 12px;
    position: relative
}

.wishlist-area tbody .product-name span:before {
    background: #505050;
    content: "";
    display: inline-block;
    height: 5px;
    margin-right: 10px;
    width: 5px
}

.wishlist-area tbody .product-price {
    color: #9e9e9e;
    font-size: 16px
}

.wishlist-area tbody .cart-plus-minus input {
    border: none;
    border-radius: 0;
    color: #464545;
    font-size: 14px;
    padding: 0 20px;
    text-align: center;
    width: 100%
}

.wishlist-area tbody .qtybutton {
    color: #9e9e9e;
    height: auto;
    line-height: normal;
    width: auto
}

.wishlist-area tbody .cart-plus {
    margin-right: 0;
    position: relative;
    width: 80px
}

.wishlist-area tbody .product-subtotal span {
    font-size: 16px;
    font-weight: 600
}

.wishlist-area tbody .product-add-to-cart span {
    color: #898989;
    display: block;
    font-size: 12px;
    margin-bottom: 5px
}

.cart-plus {
    margin-right: 20px;
    width: 110px
}

.cart-plus,
.cart-plus-minus {
    position: relative
}

.cart-plus-minus input {
    border: 1px solid #ebebeb;
    border-radius: 0;
    font-size: 16px;
    padding: 13px 30px;
    text-align: center;
    width: 100%
}

.qtybutton {
    cursor: pointer;
    font-size: 20px;
    font-weight: 400;
    height: 50px;
    left: 0;
    line-height: 50px;
    padding-inline: 2rem;
    position: absolute;
    text-align: center;
    top: 49%;
    transform: translateY(-50%);
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    width: 35px;
    z-index: 2
}

.dec.qtybutton {
    font-size: 28px
}

.inc.qtybutton {
    left: auto;
    right: 0
}

.cart-coupon form {
    align-items: center;
    display: flex
}

.wishlist-area tbody .product-subtotal span {
    color: #000 !important
}

.shop-cart-widget .title {
    border-bottom: 1px dashed #cfcfcf;
    margin-bottom: 35px;
    padding-bottom: 15px;
    text-transform: uppercase
}

.shop-cart-widget form ul li {
    align-items: flex-start;
    display: flex;
    margin-bottom: 20px
}

.shop-cart-widget form ul li:last-child {
    margin-bottom: 0
}

.shop-cart-widget .custom-control-label,
.shop-cart-widget form ul li {
    color: #2b2b2b;
    font-size: 14px;
    font-weight: 500;
    text-transform: uppercase
}

.shop-cart-widget form ul li>span {
    width: 46%
}

.shop-cart-widget .custom-control {
    margin-bottom: 10px
}

.shop-cart-widget .custom-checkbox .custom-control-label:before {
    background-color: transparent;
    border: 2px solid #bcbcbc;
    border-radius: 0;
    box-shadow: none;
    top: 4px
}

.shop-cart-widget .custom-control-label:after {
    top: 4px
}

.shop-cart-widget .custom-control-input:checked~.custom-control-label:before {
    background-color: #0465d2;
    border-color: #0465d2;
    color: #0465d2
}

.shop-cart-widget .custom-control:last-child {
    margin-bottom: 0
}

.shop-cart-widget .custom-control-input:focus:not(:checked)~.custom-control-label:before {
    border-color: #bcbcbc
}

.shop-cart-widget form ul .cart-total-amount {
    border-top: 1px dashed #cfcfcf;
    margin-top: 25px;
    padding-top: 25px
}

.shop-cart-widget form ul .cart-total-amount .amount {
    font-size: 18px
}

.shop-sidebar {
    margin-right: 25px
}

.shop-widget {
    padding: 25px 0
}

.shop-widget-title {
    margin-bottom: 20px;
    position: relative
}

.shop-widget-title .title {
    font-size: 14.5px;
    margin-bottom: 0;
    text-transform: uppercase
}

.shop-cat-list ul li {
    display: block;
    margin-bottom: 15px;
    overflow: hidden
}

.shop-cat-list ul li:last-child {
    margin-bottom: 0
}

.shop-cat-list ul li a {
    color: #696868;
    float: left;
    font-size: 14px;
    line-height: 26px;
    padding-left: 20px;
    position: relative
}

.shop-cat-list ul li a:before {
    content: "\f1ce";
    font-family: Font Awesome\ 5 Pro;
    font-size: 12px;
    font-weight: 700;
    left: 0;
    position: absolute;
    top: 0;
    transition: all .3s ease-out 0s
}

.shop-cat-list ul li span {
    background: #f2f1ef;
    border-radius: 6px;
    float: right;
    font-size: 12px;
    font-weight: 500;
    height: 26px;
    line-height: 26px;
    text-align: center;
    width: 32px
}

.sidebar-product-list ul li {
    align-items: center;
    display: flex;
    margin-bottom: 20px
}

.sidebar-product-list ul li:last-child {
    margin-bottom: 0
}

.sidebar-product-thumb {
    margin-right: 18px
}

.sidebar-product-content h5 {
    color: #111827;
    font-size: .88rem;
    font-weight: 600
}

.sidebar-product-content span {
    color: #111827;
    display: block;
    font-size: .875rem;
    font-weight: 600;
    line-height: 1
}

.breadcrumb-content .breadcrumb li.active {
    color: #111827 !important
}

.sidebar-product-content .rating {
    line-height: 1;
    margin-bottom: 8px
}

.sidebar-product-content .rating i {
    font-size: 10px
}

.shop-widget-title .slider-nav {
    position: absolute;
    right: 0;
    top: -2px;
    z-index: 1
}

.header-action {
    margin-right: 0 !important
}

.shop-widget-title .slider-nav span {
    color: #d5d2cd;
    cursor: pointer;
    transition: all .3s ease-out 0s
}

.shop-widget-title .slider-nav .slick-next {
    margin-left: 20px
}

.shop-meta-right ul li a:hover,
.shop-meta-right ul li.active a {
    background: #030712;
    border-color: #030712
}

.shop-meta-right,
.shop-meta-right ul {
    align-items: center;
    display: flex
}

.shop-meta-right ul li+li {
    margin-left: 10px
}

.shop-meta-right ul li a {
    background: #fff;
    border: 1px solid #ebebeb;
    color: #111;
    display: block;
    font-size: 24px;
    height: 50px;
    line-height: 49px;
    text-align: center;
    width: 50px
}

.shop-meta-right ul li a:hover,
.shop-meta-right ul li.active a {
    color: #fff
}

.shop-meta-right form {
    margin-left: 10px;
    min-width: 190px
}

.shop-meta-right form .custom-select {
    background: #fff url(../img/icons/nw_selarw.png) no-repeat scroll 97.5%;
    border: 1px solid #ebebeb;
    border-radius: 0;
    color: #696868;
    font-size: 14px;
    height: 50px;
    width: 100%
}

.product-review-form p {
    margin-bottom: 20px
}

.product-review-form .form-grp label {
    font-weight: 500;
    margin-bottom: 10px;
    text-transform: uppercase
}

.product-review-form .form-grp label {
    color: #5b5b5b;
    font-size: 12px
}

.product-review-form .form-grp input,
.product-review-form .form-grp textarea {
    border: 1px solid #e3e3e3;
    display: block;
    padding-block: 13px;
    width: 100%
}

.product-review-form .form-grp select {
    cursor: pointer;
    height: 50px
}

.product-review-form .form-grp textarea {
    height: 135px;
    max-height: 135px
}

.product-review-form .form-grp {
    margin-bottom: 25px
}

.shop-details-price h2 {
    color: #0a2540;
    margin-bottom: 20px
}

.shop-details-price h2 del {
    color: #b2b2b2;
    font-size: 16px;
    margin-left: 10px
}

.shop-details-content p {
    margin-bottom: 10px
}

.title-with-lines h4 {
    font-size: 20px
}

.perched-info {
    align-items: center;
    display: flex;
    flex-wrap: wrap
}

.perched-info .cart-plus {
    margin-bottom: 10px;
    margin-right: 15px;
    width: 120px
}

.perched-info .add-card-btn {
    background: #00a8b3;
    margin-bottom: 10px;
    padding: 20px 27px 16px;
    width: 220px
}

.pagination-wrap ul {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    padding: 25px 20px 15px
}

.pagination-wrap ul li {
    margin: 10px 2.5px
}

.pagination-wrap ul li.prev {
    margin-right: 20px
}

.pagination-wrap ul li.next {
    margin-left: 20px
}

.pagination-wrap ul li.next a,
.pagination-wrap ul li.prev a {
    border-radius: 0;
    height: auto;
    line-height: normal;
    width: auto
}

.pagination-wrap ul li a {
    background: transparent;
    color: #696969;
    display: block;
    font-weight: 600;
    text-align: center
}

.checkout-wrap .title {
    font-size: 20px;
    margin-bottom: 35px;
    text-transform: uppercase
}

.checkout-form .form-grp {
    margin-bottom: 12px
}

.checkout-form .form-grp label {
    color: #2b2b2b;
    display: block;
    font-size: 12px;
    font-weight: 500;
    line-height: 1;
    margin-bottom: 15px;
    text-transform: uppercase
}

.checkout-form .form-grp input {
    border: 1px solid #e2e8f0 !important;
    width: 100%
}

.checkout-form .form-grp label span {
    color: #f7ba01
}

.checkout-form .form-grp label small {
    color: #797979;
    font-size: 12px;
    font-weight: 500
}

.checkout-form .form-grp .custom-select {
    background: url(../img/icons/nw_selarw.png) no-repeat scroll 97.5%;
    border: 1px solid #dcdcdc;
    border-radius: 0;
    color: #696868;
    font-size: 14px;
    height: 53px;
    width: 100%
}

.checkout-form .form-grp textarea {
    border: 1px solid #dcdcdc;
    min-height: 120px;
    padding: 20px;
    width: 100%
}

.checkout-form .form-grp textarea::-moz-placeholder {
    color: #858585
}

.checkout-form .form-grp textarea::placeholder {
    color: #858585
}

.payment-terms {
    border-top: 1px dashed #cfcfcf;
    margin-top: 28px;
    padding-top: 28px
}

.checkout-sidebar .shop-cart-widget form ul {
    margin-bottom: 55px
}

.cmi-terms .custom-control-label,
.payment-terms .custom-control-label,
.paypal-terms .custom-control-label {
    color: #666;
    font-size: 13px;
    font-weight: 400;
    line-height: 24px;
    text-transform: none
}

.cmi-terms .custom-control-label,
.payment-terms .custom-checkbox .custom-control-label:before,
.payment-terms .custom-control-label:after,
.paypal-terms .custom-control-label:after {
    top: 5px
}

.newsletter__wrap {
    background-attachment: fixed;
    background-color: var(--bs-gray-200);
    background-position: 50%;
    background-repeat: no-repeat;
    background-size: cover;
    padding-bottom: 40px;
    padding-top: 40px
}

.newletter_input {
    background-color: hsla(0, 0%, 100%, 0);
    border-style: solid;
    font-size: 14px;
    line-height: 18px;
    padding: 17px 16px 17px 20px
}

.newletter_input::-moz-placeholder {
    color: var(--bs-gray-500)
}

.newletter_input::placeholder {
    color: var(--bs-gray-500)
}

.newletter_form {
    display: flex;
    flex-wrap: wrap;
    margin-left: auto;
    margin-right: auto;
    max-width: 100%;
    width: 600px
}

.limited-offer-title .title {
    color: #fff
}

.terms-and-conditions-wrap h5 {
    font-size: 18px;
    font-weight: 500;
    margin-bottom: 15px
}

.terms-and-conditions-wrap {
    border-bottom: 1px solid #ececec;
    margin-bottom: 30px;
    padding-bottom: 13px
}

.terms-and-conditions-wrap:last-child {
    border: none;
    margin-bottom: 0;
    padding-bottom: 0
}

.terms-and-conditions-wrap ul li {
    line-height: 2;
    margin-bottom: 10px;
    padding-left: 20px;
    position: relative
}

.terms-and-conditions-wrap ul li:last-child {
    margin-bottom: 0
}

.terms-and-conditions-wrap ul li:before {
    background: #252525;
    border-radius: 50%;
    content: "";
    height: 6px;
    left: 0;
    position: absolute;
    top: 11px;
    width: 6px
}

.error_txt {
    color: #030712;
    font-size: 150px;
    font-weight: 600;
    line-height: 1;
    margin-bottom: 20px
}

.error-content h5 {
    font-weight: 500;
    margin-bottom: 15px
}

.error-content p {
    margin-bottom: 0
}

.error-content .btn-fill-out {
    background: #030712
}

.fr-345 h5 {
    color: #fff
}

.newsletter-title h4 {
    color: #fff;
    font-size: 20px;
    margin-bottom: 5px;
    text-transform: capitalize
}

.newsletter-title span {
    color: #fff
}

.newsletter-title>span {
    display: block;
    font-size: 14px
}

.newsletter-form form {
    position: relative
}

.footer-text p {
    padding-right: 50px
}

.footer-social ul {
    display: flex;
    flex-wrap: wrap
}

.footer-social ul li {
    margin-right: 20px
}

.footer-social ul li a {
    color: #19110b;
    display: block;
    font-size: 16px;
    line-height: 1
}

.fw-title h5 {
    font-size: 16px;
    margin-bottom: 0;
    text-transform: uppercase
}

.fw-link ul li {
    display: block;
    margin-bottom: 13px
}

.fw-link ul li:last-child {
    margin-bottom: 0
}

.fw-link ul li a:hover {
    text-decoration: underline !important;
    text-underline-offset: 2px !important
}

.footer-contact ul li {
    align-items: baseline;
    color: #7d7d7d;
    display: flex;
    font-size: 15px;
    line-height: 28px;
    margin-bottom: 10px
}

.footer-contact ul li i {
    margin-right: 10px
}

.footer-alphabet span {
    display: inline-block;
    margin-bottom: 5px;
    margin-right: 15px
}

.footer-alphabet span>a {
    color: #7d7d7d
}

.copyright-wrap {
    background: #303030;
    padding: 16px 0
}

.copyright-text p {
    color: #939292;
    margin-bottom: 0
}

.copyright-text p a {
    color: #f7ba01
}

.footer-top {
    background: var(--bs-gray-100)
}

.promo-popup .modal-dialog {
    max-width: 670px;
    position: relative
}

.promo-popup .modal-content {
    background-color: transparent;
    border: none;
    border-radius: 0;
    box-shadow: none !important
}

.promo-popup .modal-body {
    padding: 0;
    position: relative
}

.popup-title .sub-title {
    color: #000;
    display: inline-block;
    font-size: 14px;
    margin-bottom: 15px;
    padding: 0 25px;
    position: relative;
    text-transform: uppercase
}

.popup-title .sub-title:after,
.popup-title .sub-title:before {
    background: #000;
    content: "";
    height: 3px;
    left: 0;
    position: absolute;
    top: 6px;
    width: 15px
}

.popup-title .sub-title:after {
    left: auto;
    right: 0
}

.popup-title .title {
    font-size: 30px;
    margin-bottom: 12px
}

.promo-subscribe .subscribe-text {
    line-height: 24px;
    margin-bottom: 35px;
    padding: 0 60px
}

.promo-form form>input {
    background: #fff;
    border: 1px solid #e3e3e3;
    margin-bottom: 20px;
    padding: 13px 20px;
    width: 100%
}

.promo-form form>input::-moz-placeholder {
    color: #adadad
}

.promo-form form>input::placeholder {
    color: #adadad
}

.total__table td {
    padding: 16px 0
}

.promo-form form .comment-check-box {
    align-items: flex-start;
    display: flex;
    margin-bottom: 60px;
    margin-top: 18px;
    text-align: left
}

.promo-form form .comment-check-box label {
    color: #252525;
    font-size: 14px;
    font-weight: 600;
    margin-top: 2px;
    text-transform: uppercase
}

.promo-popup .promo-text {
    left: 0;
    max-width: 100%;
    padding: 80px 55px 35px;
    position: absolute;
    right: 0;
    text-align: center;
    top: 0
}

.promo-popup .section-title h2 {
    font-size: 60px;
    line-height: 1.2;
    margin-bottom: 0
}

.promo-close {
    background: transparent;
    border: none;
    color: #6a6a6a;
    font-size: 22px;
    line-height: 1;
    padding: 5px;
    position: absolute;
    right: 22px;
    top: 22px;
    transform: rotate(45deg);
    z-index: 1
}

.popup-title .title span,
.promo-close:hover {
    color: #030712
}

.promo-popup img {
    width: 100%
}

#preloader {
    background-color: #fff
}

.ctn-preloader {
    align-items: center;
    cursor: default;
    display: flex;
    height: 100%;
    justify-content: center;
    left: 0;
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 9000
}

.ctn-preloader .animation-preloader {
    z-index: 1000
}

.ctn-preloader .animation-preloader .spinner {
    animation: spinner 1s linear infinite;
    border: 3px solid rgba(0, 0, 0, .2);
    border-radius: 50%;
    border-top-color: #030712;
    height: 150px;
    margin: 0 auto 3.5em;
    width: 150px
}

.ctn-preloader .animation-preloader .txt-loading {
    font: 700 5em Rubik, sans-serif;
    text-align: center;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.ctn-preloader .animation-preloader .txt-loading .letters-loading {
    color: rgba(0, 0, 0, .2);
    position: relative
}

.ctn-preloader .animation-preloader .txt-loading .letters-loading:before {
    animation: letters-loading 4s infinite;
    color: #000;
    content: attr(data-text-preloader);
    left: 0;
    opacity: 0;
    position: absolute;
    top: -3px;
    transform: rotateY(-90deg)
}

.ctn-preloader .animation-preloader .txt-loading .letters-loading:nth-child(2):before {
    animation-delay: .2s
}

.ctn-preloader .animation-preloader .txt-loading .letters-loading:nth-child(3):before {
    animation-delay: .4s
}

.ctn-preloader .animation-preloader .txt-loading .letters-loading:nth-child(4):before {
    animation-delay: .6s
}

.ctn-preloader .animation-preloader .txt-loading .letters-loading:nth-child(5):before {
    animation-delay: .8s
}

.ctn-preloader .animation-preloader .txt-loading .letters-loading:nth-child(6):before {
    animation-delay: 1s
}

.ctn-preloader .animation-preloader .txt-loading .letters-loading:nth-child(7):before {
    animation-delay: 1.2s
}

.ctn-preloader .animation-preloader .txt-loading .letters-loading:nth-child(8):before {
    animation-delay: 1.4s
}

.ctn-preloader.dark .animation-preloader .spinner {
    border-color: #fff hsla(0, 0%, 100%, .2) hsla(0, 0%, 100%, .2)
}

.ctn-preloader.dark .animation-preloader .txt-loading .letters-loading {
    color: hsla(0, 0%, 100%, .2)
}

.ctn-preloader.dark .animation-preloader .txt-loading .letters-loading:before {
    color: #fff
}

.ctn-preloader p {
    color: #3b3b3b;
    font-size: 14px;
    font-weight: 500;
    letter-spacing: 8px;
    text-transform: uppercase
}

.ctn-preloader .loader {
    font-size: 0;
    height: 100%;
    left: 0;
    pointer-events: none;
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1
}

.ctn-preloader .loader .row {
    height: 100%
}

.ctn-preloader .loader .loader-section {
    padding: 0
}

.ctn-preloader .loader .loader-section .bg {
    background-color: #fff;
    height: 100%;
    left: 0;
    transition: all .8s cubic-bezier(.77, 0, .175, 1);
    width: 100%
}

.ctn-preloader.loaded .animation-preloader {
    opacity: 0;
    transition: .3s ease-out
}

.ctn-preloader.loaded .loader-section .bg {
    transition: allcubic-bezier(.1, .1, .1, 1) .7s .3s;
    width: 0
}

@keyframes spinner {
    to {
        transform: rotate(1turn)
    }
}

@keyframes letters-loading {
    0%,
    75%,
    to {
        opacity: 0;
        transform: rotateY(-90deg)
    }
    25%,
    50% {
        opacity: 1;
        transform: rotateY(0deg)
    }
}

@media screen and (max-width:767px) {
    .ctn-preloader .animation-preloader .spinner {
        height: 8em;
        width: 8em
    }
    .ctn-preloader .animation-preloader .txt-loading {
        font: 700 3.5em Rubik, sans-serif
    }
}

@media screen and (max-width:500px) {
    .ctn-preloader .animation-preloader .spinner {
        height: 7em;
        width: 7em
    }
    .ctn-preloader .animation-preloader .txt-loading {
        font: 700 2em Rubik, sans-serif
    }
    .scroll-top.open {
        bottom: 125px
    }
}

[dir=rtl] .breadcrumb-item+.breadcrumb-item:before {
    padding-inline: 8px 2px !important;
    transform: scaleX(-1) !important
}

.single-variants .single-variant .textual-buttons-container {
    margin: 0 0 -6px
}

[dir=rtl] .textual-button:not(:last-child) {
    margin: 0 0 0 6px
}

[dir] .textual-button label {
    background-color: #fff;
    border: 1px solid #e5e5e5;
    border-radius: 3px;
    cursor: pointer
}

[dir=ltr] .textual-button [type=radio] {
    left: -9999px
}

[dir=rtl] .textual-button [type=radio] {
    right: -9999px
}

.textual-button [type=radio]:checked+label {
    color: #fff;
    font-weight: 500
}

.single-variants .single-variant .option-name {
    display: block;
    font-size: 15px;
    font-weight: 500;
    margin-bottom: 5px
}

[dir] .single-variants .single-variant:not(:last-child) {
    margin-bottom: 20px
}

[dir] .single-variants .single-variant .option-name {
    margin: 0 0 10px
}

.single-variants .single-variant .textual-buttons-container {
    align-items: center;
    display: flex;
    flex-wrap: wrap
}

[dir] .single-variants .single-variant .textual-buttons-container {
    margin: 0 0 -6px
}

[dir] .textual-button {
    margin: 0 0 6px
}

[dir=ltr] .textual-button:not(:last-child) {
    margin: 0 6px 6px 0
}

[dir=rtl] .textual-button:not(:last-child) {
    margin: 0 0 6px 6px
}

.textual-button [type=radio] {
    position: absolute
}

.single-product {
    color: #000;
    padding-bottom: 20px;
    padding-top: 0
}

.wishlist-area .table {
    border: 1px solid #b6b6b652
}

.wishlist-img-product {
    margin: 0 10px
}

@media(max-width:768px) {
    .shop-cart-area .table,
    .wishlist-area table {
        max-width: 100% !important;
        width: 100% !important
    }
    .shop-cart-area .table thead,
    .wishlist-area .table thead {
        display: none
    }
    .shop-cart-area .table tbody,
    .wishlist-area .table tbody {
        display: flex;
        flex-direction: column;
        -ms-flex-wrap: wrap;
        flex-wrap: wrap;
        width: 100%
    }
    .shop-cart-area .table tbody tr,
    .wishlist-area .table tbody tr {
        border-radius: 2px;
        display: block;
        display: flex;
        flex-direction: column;
        margin-bottom: 1rem;
        padding: 1rem 0;
        position: relative;
        text-align: center;
        width: 100%
    }
    .shop-cart-area .table tbody tr td,
    .wishlist-area .table tbody tr td {
        border: 0;
        color: #252525;
        font-size: 13px;
        padding: 0 15px
    }
    .wishlist-area tbody .cart-plus {
        border: 1px solid #eee;
        border-radius: 5px;
        padding: 5px 6px
    }
    .shop-cart-area .product-subtotal,
    .wishlist-area .product-price,
    .wishlist-area .product-subtotal {
        display: none
    }
    .shop-cart-area .product-quantity,
    .wishlist-area .product-quantity {
        align-items: center;
        display: flex;
        justify-content: space-between;
        padding: 8px 20px
    }
    .shop-cart-area .product-name,
    .wishlist-area .product-name {
        border-bottom: 1px solid #eee !important;
        margin-bottom: 1rem !important;
        padding-bottom: 1rem !important
    }
    .shop-cart-area .product-quantity:after,
    .wishlist-area .product-quantity:after {
        content: attr(data-title);
        flex-shrink: 0;
        font-weight: 500
    }
    .shop-cart-area .product-thumbnail img,
    .wishlist-area .product-thumbnail img {
        margin-bottom: 10px;
        width: 60px
    }
    .wishlist-area .product-add-to-cart {
        align-content: center;
        align-items: center;
        display: flex;
        justify-content: space-between
    }
    .shop-cart-area .product-thumbnail {
        align-items: center;
        display: flex;
        flex-direction: column;
        justify-content: center
    }
    .wishlist-area .favorite-remove {
        position: absolute;
        right: 5px;
        top: 5px
    }
    .shop-cart-area .product-thumbnail .wishlist-remove {
        align-items: center;
        display: flex;
        justify-content: space-between;
        margin-bottom: 15px;
        margin-top: -11px;
        padding: 0 9px;
        width: 100%
    }
    .shop-cart-area .product-thumbnail .wishlist-remove:before {
        color: #666;
        content: attr(data-title);
        flex-shrink: 0;
        font-size: 12px;
        font-weight: 400
    }
    .shop-cart-area tbody .product-name span {
        display: none
    }
}

@media (min-width:992px) {
    .search-modal.modal-lg {
        max-width: 650px
    }
}

@media screen and (max-width:600px) {
    .search-modal.modal-lg {
        max-width: 490px
    }
}

.radio-variant {
    align-items: center;
    display: grid;
    font-size: 16px;
    gap: .5em;
    grid-template-columns: 1em auto;
    line-height: .8;
    margin-bottom: 5px;
    margin-right: 8px !important
}

.radio-variant-box {
    border-radius: .2rem !important;
    cursor: pointer;
    display: inline-block;
    height: 26px;
    margin-bottom: 5px;
    margin-inline: 2px;
    min-width: 26px;
    outline: 1px solid #dddddddc;
    outline-offset: 2px;
    padding: 2px 4px !important;
    text-align: center;
    text-transform: capitalize !important;
    transition: all .2s ease
}

.radio-variant-box:hover,
input.radio-input:checked+label.radio-variant-box {
    outline: 1px solid var(--bs-primary)
}

.radio-variant-box:after,
.radio-variant-box:before {
    display: none !important;
    scale: 0 !important
}

.radio-variant:focus-within {
    color: #000
}

.radio-variant input[type=radio] {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-color: #fff;
    border: .1em solid #9c9494;
    border-radius: 50%;
    display: grid;
    font: inherit;
    height: 1.15em;
    margin: 0;
    place-content: center;
    transform: translateY(-.075em);
    width: 1.15em
}

.radio-variant input[type=radio]:before {
    background-color: #2a84ff;
    border-radius: 50%;
    box-shadow: inset 1em 1em #2a84ff;
    content: "";
    height: .65em;
    transform: scale(0);
    transition: transform .12s ease-in-out;
    width: .65em
}

.radio-variant input[type=radio]:checked:before {
    transform: scale(1)
}

.phoneNumberPicker {
    align-content: center;
    align-items: center;
    border: 1px solid #000;
    display: flex;
    overflow: visible !important;
    position: relative
}

.phoneNumberPicker .btn-select {
    align-items: center;
    border-radius: 0;
    display: flex;
    height: 100%;
    line-height: 0rem;
    margin: 0;
    padding-inline: 18px
}

.phoneNumberPicker .btn-select img {
    width: 32px
}

.phoneNumberPicker #phoneNumber {
    border: none !important
}

.phoneNumberPicker #select-selected {
    border: 1px solid var(--bs-gray-200);
    box-shadow: 0 8px 20px 9px rgb(20 32 44/8%) !important
}

.text-red {
    color: #f5365c !important;
    font-size: 14px
}

a.text-red:focus,
a.text-red:hover {
    color: #ec0c38 !important
}

header {
    background-color: #fff;
    border-bottom: 1px solid var(--bs-gray-300)
}

.navbar-brand-logo {
    align-items: center;
    display: flex;
    justify-content: center
}

.icon-item {
    padding: 0 5px
}

.fill-black {
    fill: #000c2a;
    width: 19px
}

.side-responsive-nav {
    background: #fff;
    border-left: 1px solid #f0f0f0;
    bottom: 0;
    left: -350px;
    position: fixed;
    top: 0;
    transform: translate(0);
    transition: transform .25s;
    width: 350px;
    z-index: 4;
    z-index: 999
}

.side-responsive-nav.active {
    transform: translate(100%)
}

.fs-22 {
    font-size: 22px
}

.fs-18 {
    font-size: 18px
}

.fs-14 {
    font-size: 14px
}

.cart-footer {
    border-top: 1px solid var(--bs-gray-300) !important;
    bottom: 0;
    padding: 15px;
    position: absolute;
    width: 100%
}

.cart-actions .d-grid a {
    margin-top: 0 !important
}

.side-cart-summary .cart-body {
    overflow-y: auto;
    padding: 0 15px
}

.my-card-img {
    border: 1px solid #f0f0f0;
    width: 60px
}

.img-fit {
    height: 100%;
    -o-object-fit: fill;
    object-fit: fill;
    width: 100%
}

.item-card-body {
    display: flex;
    margin: 0 0 0 10px;
    position: relative;
    width: calc(100% - 60px)
}

.item-details h3 {
    font-size: 16px;
    line-height: 22px
}

.item-details h3 a {
    color: inherit;
    text-decoration: none
}

.quantity small {
    background-color: #fafafa;
    border: 1px solid #e5e5e5;
    border-radius: 3px;
    margin: 0 8px;
    padding: 0 5px;
    text-align: center
}

.cursor-pointer {
    cursor: pointer
}

.resp-toggle {
    display: none
}

.nav-cart-link {
    position: relative
}

.cart-count:not(.inline) {
    background-color: #111827;
    border-radius: 50%;
    color: #fff;
    display: inline-block;
    font-size: 13px;
    font-weight: 700;
    height: 20px;
    line-height: 20px;
    position: absolute;
    text-align: center;
    top: 0;
    width: 20px
}

[dir=ltr] .cart-count {
    left: -5px
}

[dir=rtl] .cart-count {
    right: -5px
}

[dir=ltr] .wishlist-count {
    left: -8px
}

[dir=rtl] .wishlist-count {
    right: -8px
}

.resp-brand {
    text-align: center
}

.brand-fit {
    height: 100%;
    -o-object-fit: fill;
    object-fit: fill;
    width: auto
}

.resp-navigation {
    list-style: none;
    margin: 0;
    padding: 0
}

.resp-navigation a {
    color: initial;
    text-decoration: none;
    width: 100%
}

.resp-nav-list {
    border-bottom: 1px solid #e2e2e2;
    position: relative
}

.resp-nav-list a {
    display: inline-block;
    font-size: 15px;
    padding: 15px 20px;
    text-transform: capitalize
}

.resp-nav-list:last-child {
    border-bottom: none
}

.resp-nav-arrow {
    position: absolute;
    top: 14px
}

[dir=ltr] .resp-nav-arrow {
    right: 10px
}

[dir=rtl] .resp-nav-arrow {
    left: 5px
}

.resp-nav-child-list {
    border-top: 1px solid #e2e2e2
}

.resp-nav-child-list a {
    display: inline-block;
    padding: 15px 10px
}

.resp-nav-child {
    list-style: none;
    margin: 0;
    padding: 0
}

@keyframes slide-down {
    0% {
        transform: translateY(-100%)
    }
    to {
        transform: translateY(0)
    }
}

@media (max-width:992px) {
    .icon-item {
        padding: 0 4px
    }
    .icon-item .cart-price {
        display: none
    }
    .navbar-brand-logo img {
        height: 28px
    }
    .resp-toggle {
        display: block
    }
    .cart-count,
    .wishlist-count {
        left: -10px
    }
}

.side-responsive-nav .side-resp-body {
    height: calc(100% - 70px);
    overflow-y: auto
}

.header-wrapper {
    position: relative;
    z-index: 200
}

.search-wrapper {
    background: #ededed;
    bottom: -77px;
    left: 0;
    position: absolute;
    right: 0;
    transition: all .25s ease;
    visibility: hidden;
    z-index: 2
}

.search-wrapper.active {
    visibility: visible
}

.bg-dropify {
    background-color: #0098ff
}

.header__link {
    background: #fff;
    border-radius: 50px;
    color: #1b1b1b;
    display: inline-block;
    font-weight: 400;
    margin: 10px 0;
    text-transform: capitalize;
    transition: all .2s ease-in-out
}

.header__link:hover {
    text-decoration: underline !important;
    text-underline-offset: 2px !important
}

.breadcrumb-content .breadcrumb li.active {
    color: #1b1b1b
}

@media (max-width:768px) {
    .search-wrapper {
        bottom: -168px
    }
}

.core-features-area .card.card-body {
    border-color: var(--bs-gray-200) !important
}

.core-features-item:hover .core-features-content h6 {
    color: #111827
}

.item-details {
    padding: 0 20px 0 0
}

.delete-cart-item {
    color: initial;
    position: absolute;
    top: 23px
}

.delete-cart-item svg {
    width: 14px
}

[dir=ltr] .delete-cart-item {
    right: 0
}

[dir=rtl] .delete-cart-item {
    left: 0
}

.edit-cart-item {
    color: initial;
    position: absolute;
    top: 0
}

.edit-cart-item svg {
    width: 14px
}

[dir=ltr] .edit-cart-item {
    right: 0
}

[dir=rtl] .edit-cart-item {
    left: 0
}

[dir=rtl] .qty-value {
    margin-right: 10px
}

[dir=ltr] .qty-value {
    margin-left: 10px
}

.qty-box {
    background: #f2f2f2;
    border: 1px solid #d9d9d9;
    display: inline-block;
    font-size: 12px;
    padding: 1px 7px
}

[dir=rtl] .qty-box {
    margin-right: 5px
}

[dir=ltr] .qty-box {
    margin-left: 5px
}

.summary-update-qty input[type=number]::-webkit-inner-spin-button,
.summary-update-qty input[type=number]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0
}

.summary-update-qty input[type=number] {
    -moz-appearance: textfield;
    border: 1px solid #e5e5e5;
    border-radius: 3px;
    font-size: 13px;
    font-weight: 600;
    height: 20px;
    margin: 0 5px;
    padding: 0;
    text-align: center;
    width: 24px
}

.summary-submit-qty {
    background: transparent;
    border: none
}

.summary-submit-qty svg {
    width: 13px
}

[dir=rtl] span.summary-update-qty {
    margin-right: 5px
}

[dir=ltr] span.summary-update-qty {
    margin-left: 5px
}

.top-offer-wrap {
    background: #000;
    padding: 15px 0
}

.top-offer-wrap p {
    color: #fff;
    font-size: 14px;
    margin: 0;
    padding: 0
}

.copyright-wrap {
    background: #000
}

.copyright-text p {
    color: #fff;
    font-size: 12px
}

.breadcrumb-content .breadcrumb {
    padding: 10px
}

.cart-plus-minus input {
    border-radius: 8px
}

.limited-offer-title .sub-title,
.section-title .sub-title {
    font-size: 14px
}

.review-cart .prt {
    color: #4a4a4a;
    font-size: 14px
}

.check-express-checkout-form .qtybutton {
    top: 25px
}

.button_whatssap {
    font-size: 17px;
    margin-top: 10px
}

.navbar-brand-logo img {
    max-height: 60px;
    width: auto
}

.footer-logo img {
    max-height: 80px;
    width: auto
}

.pwrap-4573 {
    background: #fff;
    border: 1px solid #ffd688;
    height: 100%;
    padding: 7px
}

.pwrap-4573:hover {
    background-color: #fff;
    box-shadow: 0 0 8px 0 rgba(0, 0, 0, .12)
}

.pthumb-3497 {
    background: #f7f7f7;
    height: 308px;
    position: relative
}

.pdetail-3978 a {
    align-items: center;
    color: initial;
    display: flex;
    justify-content: center;
    text-decoration: none
}

.pdetail-3978 .title {
    font-size: 16px;
    font-weight: 400
}

.pdetail-3978 {
    position: relative
}

.card__variants {
    bottom: 8px;
    left: 0;
    margin: 0;
    padding: 0;
    position: absolute;
    right: 0
}

.pwrap-4573:hover .card__variants {
    height: 39px;
    padding: 0 5px;
    transform: scaleY(1)
}

.card__content {
    padding: 10px
}

.review-cart i {
    font-size: 12px
}

.img-fill {
    height: 100%;
    -o-object-fit: fill;
    object-fit: fill;
    width: 100%
}

.add-cart-589 {
    align-items: center;
    background: #ffa800;
    border-radius: 50px;
    display: flex;
    font-size: 15px;
    height: 40px;
    justify-content: center;
    width: 40px
}

.add-cart-589:hover {
    opacity: .8
}

.add-cart-589 i {
    color: #fff
}

.new-654 {
    color: #111827;
    font-size: .875rem;
    font-weight: 700
}

.old-654 {
    color: #8e8e8e;
    font-size: .875rem;
    text-decoration: line-through
}

.per-654 {
    color: #38ae04;
    font-size: 13px
}

.plike-867 {
    left: 15px
}

.plike-867 {
    font-size: .875rem;
    position: absolute;
    top: 10px;
    z-index: 5
}

.plike-867 i {
    color: #252525
}

.img-cover {
    height: 100%;
    -o-object-fit: cover;
    object-fit: cover;
    width: 100%
}

.review-cart {
    align-items: center;
    display: flex !important;
    gap: .25rem !important;
    margin-bottom: 5px
}

.review-5467 {
    background: #fafafa;
    border: 2px solid #bcbcbc;
    border-radius: 15px;
    height: 100%;
    overflow: hidden;
    transition: all .3s ease
}

.review-5467:hover {
    border-color: #1b1b1b
}

.thumb-9087 {
    background: #f7f7f7;
    height: 308px;
    position: relative
}

.starts-234 {
    background: #fff;
    border: 1px solid #b1b1b1;
    border-radius: 50px;
    display: inline-block;
    left: 10px;
    padding: 1px 10px;
    position: absolute;
    top: 5px
}

.starts-234 i {
    color: #ffa800 !important;
    font-size: 11px
}

.detail-089 {
    padding: 10px 12px
}

.detail-089 .name {
    color: #252525;
    font-size: 17px;
    margin: 0
}

.detail-089 .city {
    color: #212121;
    font-size: 14px
}

.parag-6709 {
    margin-top: 10px
}

.parag-6709 p {
    color: #252525;
    margin: 0;
    text-align: center
}

.sticky-wrap {
    display: block;
    position: sticky;
    top: 96px;
    width: 100%
}

.category__wrap {
    background-color: var(--bs-gray-200);
    color: var(--bs-gray-900) !important;
    display: flex;
    flex-direction: column;
    height: 300px;
    overflow: hidden;
    position: relative
}

.icon {
    align-items: center;
    background: #000;
    border-radius: 50%;
    display: flex;
    height: 35px;
    justify-content: center;
    width: 35px
}

.icon svg {
    height: 16px !important;
    width: 16px !important;
    fill: #fff !important
}

.carousel-indicators li {
    background-color: #000
}

.review-cart i {
    color: #ffa800
}

.best-cat-border {
    border-bottom: initial
}

.phoneNumberPicker .btn-select {
    justify-content: center
}

.border_inputs {
    border-radius: 5px;
    overflow: hidden
}

.wishlist-count {
    background-color: #111827;
    border-radius: 50%;
    color: #fff;
    display: inline-block;
    font-size: 13px;
    font-weight: 700;
    height: 20px;
    line-height: 20px;
    position: absolute;
    text-align: center;
    top: 0;
    width: 20px
}

.fr-345 i {
    display: none
}

.fr-345 {
    margin-bottom: 30px
}

.fr-688 {
    margin-bottom: 50px
}

.footer-social ul li a {
    font-size: 19px
}

@media (max-width:992px) {
    .fr-345 {
        align-items: center;
        cursor: pointer;
        display: flex;
        font-size: 15px;
        justify-content: space-between;
        margin-bottom: 0;
        padding: 15px 10px
    }
    .fr-345 i {
        display: block
    }
    .fr-345 h5 {
        font-size: 17px;
        margin: 0;
        padding: 0
    }
    .fr-688 {
        background: #393939;
        margin-bottom: 10px
    }
    .fr-132 {
        border-top: 1px solid #000;
        display: none;
        font-size: 14px;
        padding: 15px 10px;
        transition: all .5s ease
    }
    .svg-social {
        height: 25px;
        width: 25px
    }
    .fr-132.show {
        display: block;
        height: auto
    }
    .rotate {
        transform: rotate(-90deg)
    }
}

.resp-brand img {
    height: 100%;
    max-height: 60px;
    width: auto
}

.bg-light-dropify {
    background-color: rgba(255, 96, 0, .1)
}

.plike-867.active i {
    color: #fa3a3a
}

.text-end {
    text-align: end
}

.text-start {
    text-align: start
}

.blog-post-content h4 a {
    font-size: 20px
}

.rollUpQuickBuyWrap .product-rollup {
    display: flex;
    justify-content: center;
    margin: 0 auto;
    max-width: 200px;
    text-align: center
}

.rollUpQuickBuyWrap .product-rollup .product-rollup-variant {
    display: inline-block;
    flex-basis: 25%;
    padding: 0;
    width: 25%
}

.rollUpQuickBuyWrap .product-rollup .product-rollup-variant label {
    background-color: bisque;
    border-radius: 50px;
    display: inline-block;
    height: 15px;
    margin: 0;
    position: relative;
    width: 15px
}

.errorMsg {
    margin-block-start: .5rem !important
}

.variant-checked label {
    box-shadow: inset 0 0 1px 1px #fff, 0 0 1px 1px #0000007d
}

.none {
    display: none
}

.textual-button label {
    border-radius: 9999px !important
}

.add-cart-89 {
    border: none;
    border-radius: .375rem;
    color: #fff;
    font-size: .875rem;
    font-weight: 600;
    line-height: 1.25rem;
    padding: .5rem 2rem;
    transition: background-color .3s ease
}

.add-cart-89,
.add-cart-89:hover {
    background-color: #111827
}

.card-variant-wrap input[type=radio] {
    margin-right: 10px;
    transform: scale(1.5)
}

.card-variant-wrap label {
    font-size: 1rem
}

.section-title .title {
    font-weight: bolder
}

.section-title .description {
    color: #3e3e3e;
    font-size: 19px;
    line-height: 30px;
    margin-top: 7px
}

.product_review__rate {
    align-items: center;
    display: flex;
    font-size: 12px;
    margin-top: .5rem
}

.bg-light {
    background-color: #fbfbfb !important
}

.post__wrap {
    background-color: #111827;
    border-radius: 1rem;
    display: flex;
    flex-direction: column;
    height: 100%;
    isolation: isolate;
    justify-content: flex-end;
    overflow: hidden;
    padding: 20rem 2rem 2rem;
    position: relative
}

.post__img {
    display: block;
    height: 100%;
    max-width: 100%;
    -o-object-fit: cover;
    object-fit: cover;
    vertical-align: middle;
    width: 100%
}

.post-block-one,
.post__img {
    inset: 0;
    position: absolute;
    z-index: -10
}

.post-block-one {
    background-image: linear-gradient(0deg, #111827, #11182700)
}

.post-block-two {
    inset: 0;
    position: absolute;
    z-index: -10
}

.post__date {
    align-items: center;
    color: #d1d5db;
    display: flex;
    flex-wrap: wrap;
    font-size: .875rem;
    line-height: 1.5rem;
    overflow: hidden;
    row-gap: .25rem
}

.post__title {
    color: #fff;
    font-size: 1.125rem;
    font-weight: 600;
    line-height: 1.5rem;
    margin: .4rem 0 0
}

.post-block-three {
    inset: 0;
    position: absolute
}

.post__title a:hover {
    color: #fff
}

.review__wrap {
    background-color: #f9fafb;
    border-radius: 1rem;
    font-size: .875rem;
    line-height: 1.5rem;
    padding: 2rem
}

.review__comment p {
    color: #111827
}

.review__caption {
    align-items: center;
    -moz-column-gap: 1rem;
    column-gap: 1rem;
    display: flex;
    margin-top: 1.5rem
}

.review__img {
    border-radius: 9999px;
    display: block;
    height: 2.5rem;
    height: auto;
    max-width: 100%;
    vertical-align: middle;
    width: 2.5rem
}

.review__name {
    color: #111827;
    font-weight: 600
}

.review__city {
    color: #4b5563
}