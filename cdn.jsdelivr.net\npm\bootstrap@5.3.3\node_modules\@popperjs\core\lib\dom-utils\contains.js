import {
    isShadowRoot
} from "./instanceOf.js";
export default function contains(parent, child) {
    var rootNode = child.getRootNode && child.getRootNode(); // First, attempt with faster native method

    if (parent.contains(child)) {
        return true;
    } // then fallback to custom implementation with Shadow DOM support
    else if (rootNode && isShadowRoot(rootNode)) {
        var next = child;

        do {
            if (next && parent.isSameNode(next)) {
                return true;
            } // $FlowFixMe[prop-missing]: need a better way to handle this...


            next = next.parentNode || next.host;
        } while (next);
    } // Give up, the result is false


    return false;
}