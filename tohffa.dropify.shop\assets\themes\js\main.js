(function($) {
    "use strict";

    /*=============================================
        =    		 Preloader			      =
    =============================================*/
    function preloader() {
        $('#ctn-preloader').addClass('loaded');
        $("#loading").fadeOut(500);

        if ($('#ctn-preloader').hasClass('loaded')) {
            $('#preloader').delay(900).queue(function() {
                $(this).remove();
            });
        }
    }
    $(window).on('load', function() {
        preloader();
        mainSlider();
        //aosAnimation();
        popupModal();
        //wowAnimation();
    });


    function formatCurrency(amount, locale = 'en-US', decimalPlaces = 2) {
        return new Intl.NumberFormat(locale, {
            style: 'decimal',
            minimumFractionDigits: decimalPlaces,
            maximumFractionDigits: decimalPlaces
        }).format(amount);
    }

    /*=============================================
            =          Custom Slider Active             =
    =============================================*/

    $('.custom-slider-active-rtl').slick({
        dots: true,
        infinite: true,
        speed: 1000,
        autoplay: false,
        centerMode: false,
        centerPadding: '0px',
        arrows: false,
        slidesToShow: 4,
        slidesToScroll: 1,
        rtl: true,
        responsive: [{
                breakpoint: 1200,
                settings: {
                    slidesToShow: 3,
                    slidesToScroll: 1,
                    infinite: true,
                }
            },
            {
                breakpoint: 992,
                settings: {
                    slidesToShow: 2,
                    slidesToScroll: 1
                }
            },
            {
                breakpoint: 767,
                settings: {
                    slidesToShow: 1,
                    slidesToScroll: 1,
                    arrows: false,
                }
            },
            {
                breakpoint: 575,
                settings: {
                    slidesToShow: 1,
                    slidesToScroll: 1,
                    arrows: false,
                }
            },
        ]
    });

    /*=============================================
        =          Custom Slider Active             =
    =============================================*/
    $('.custom-slider-active-ltr').slick({
        dots: true,
        infinite: true,
        speed: 1000,
        autoplay: false,
        centerMode: false,
        centerPadding: '0px',
        arrows: false,
        slidesToShow: 4,
        slidesToScroll: 1,
        responsive: [{
                breakpoint: 1200,
                settings: {
                    slidesToShow: 3,
                    slidesToScroll: 1,
                    infinite: true,
                }
            },
            {
                breakpoint: 992,
                settings: {
                    slidesToShow: 2,
                    slidesToScroll: 1
                }
            },
            {
                breakpoint: 767,
                settings: {
                    slidesToShow: 1,
                    slidesToScroll: 1,
                    arrows: false,
                }
            },
            {
                breakpoint: 575,
                settings: {
                    slidesToShow: 1,
                    slidesToScroll: 1,
                    arrows: false,
                }
            },
        ]
    });


    /*=============================================
        =    		Mobile Menu			      =
    =============================================*/
    //SubMenu Dropdown Toggle
    if ($('.menu-area li.dropdown ul').length) {
        $('.menu-area .navigation li.dropdown').append('<div class="dropdown-btn"><span class="fas fa-angle-down"></span></div>');

    }

    //Mobile Nav Hide Show
    if ($('.mobile-menu').length) {

        var mobileMenuContent = $('.menu-area .main-menu').html();
        $('.mobile-menu .menu-box .menu-outer').append(mobileMenuContent);

        //Dropdown Button
        $('.mobile-menu li.dropdown .dropdown-btn').on('click', function() {
            $(this).toggleClass('open');
            $(this).prev('ul').slideToggle(500);
        });
        //Menu Toggle Btn
        $('.mobile-nav-toggler').on('click', function() {
            $('body').addClass('mobile-menu-visible');
        });

        //Menu Toggle Btn
        $('.mobile-menu .menu-backdrop,.mobile-menu .close-btn').on('click', function() {
            $('body').removeClass('mobile-menu-visible');
        });
    }


    /*=============================================
        =     Menu sticky & Scroll to top      =
    =============================================*/

    function checkScroll() {
        var scroll = $(window).scrollTop();
        if (scroll < 245) {
            $("#sticky-header").removeClass("sticky-menu");
            $('.scroll-to-target').removeClass('open');

        } else {
            $("#sticky-header").addClass("sticky-menu");
            $('.scroll-to-target').addClass('open');
        }
    }
    checkScroll();
    $(window).on('scroll', function() {
        checkScroll();
    });



    /*=============================================
        =    		 Scroll Up  	         =
    =============================================*/
    if ($('.scroll-to-target').length) {
        $(".scroll-to-target").on('click', function() {
            var target = $(this).attr('data-target');
            // animate
            $('html, body').animate({
                scrollTop: $(target).offset().top
            }, 1000);

        });
    }


    /*=============================================
        =    	   Data Background  	         =
    =============================================*/
    $("[data-background]").each(function() {
        $(this).css("background-image", "url(" + $(this).attr("data-background") + ")")
    })



    /*=============================================
        =    	   Toggle Active  	         =
    =============================================*/
    $('.cat-toggle').on('click', function() {
        $('.category-menu').slideToggle(500);
        return false;
    });
    $('.more_slide_open').slideUp();
    $('.more_categories').on('click', function() {
        $(this).toggleClass('show');
        $('.more_slide_open').slideToggle();
    });



    /*=============================================
        =    		 Main Slider		      =
    =============================================*/
    function mainSlider() {
        var BasicSlider = $('.slider-active');
        BasicSlider.on('init', function(e, slick) {
            var $firstAnimatingElements = $('.single-slider:first-child').find('[data-animation]');
            doAnimations($firstAnimatingElements);
        });
        BasicSlider.on('beforeChange', function(e, slick, currentSlide, nextSlide) {
            var $animatingElements = $('.single-slider[data-slick-index="' + nextSlide + '"]').find('[data-animation]');
            doAnimations($animatingElements);
        });
        BasicSlider.slick({
            autoplay: true,
            autoplaySpeed: 10000,
            dots: false,
            rtl: isRtl,
            fade: true,
            arrows: false,
            responsive: [{
                breakpoint: 767,
                settings: {
                    dots: false,
                    arrows: false
                }
            }]
        });

        function doAnimations(elements) {
            var animationEndEvents = 'webkitAnimationEnd mozAnimationEnd MSAnimationEnd oanimationend animationend';
            elements.each(function() {
                var $this = $(this);
                var $animationDelay = $this.data('delay');
                var $animationType = 'animated ' + $this.data('animation');
                $this.css({
                    'animation-delay': $animationDelay,
                    '-webkit-animation-delay': $animationDelay
                });
                $this.addClass($animationType).one(animationEndEvents, function() {
                    $this.removeClass($animationType);
                });
            });
        }
    }


    /*=============================================
        =    		Top Selling Active		     =
    =============================================*/
    $('.top-selling-active').owlCarousel({
        loop: true,
        margin: 15,
        items: 4,
        autoplay: false,
        autoplayTimeout: 5000,
        autoplaySpeed: 1000,
        navText: ['<i class="fa fa-angle-left"></i>', '<i class="fa fa-angle-right"></i>'],
        nav: true,
        dots: false,
        responsive: {
            0: {
                items: 1,
                center: false,
                nav: false,
            },
            575: {
                items: 2,
                center: false,
                nav: false,
            },
            768: {
                items: 3,
                center: false,
            },
            992: {
                items: 4,
                center: false,
            },
            1200: {
                items: 4
            },
        }
    })


    /*=============================================
        =    		Popular Active		      =
    =============================================*/
    $('.popular-active').slick({
        dots: false,
        infinite: true,
        speed: 1000,
        autoplay: true,
        arrows: true,
        centerMode: false,
        prevArrow: '<button type="button" class="slick-prev"><i class="fas fa-angle-left"></i></button>',
        nextArrow: '<button type="button" class="slick-next"><i class="fas fa-angle-right"></i></button>',
        slidesToShow: 4,
        slidesToScroll: 1,
        responsive: [{
                breakpoint: 1200,
                settings: {
                    slidesToShow: 3,
                    slidesToScroll: 1,
                    infinite: true,
                }
            },
            {
                breakpoint: 992,
                settings: {
                    slidesToShow: 2,
                    slidesToScroll: 1,
                    arrows: false,
                }
            },
            {
                breakpoint: 767,
                settings: {
                    slidesToShow: 2,
                    slidesToScroll: 1,
                    arrows: false,
                }
            },
            {
                breakpoint: 575,
                settings: {
                    slidesToShow: 1,
                    slidesToScroll: 1,
                    arrows: false,
                }
            },
        ]
    });


    /*=============================================
        =    		Deal Day Active		      =
    =============================================*/
    $('.deal-day-active').slick({
        dots: false,
        infinite: true,
        speed: 1000,
        autoplay: true,
        arrows: false,
        slidesToShow: 3,
        slidesToScroll: 1,
        responsive: [{
                breakpoint: 1200,
                settings: {
                    slidesToShow: 2,
                    slidesToScroll: 1,
                    infinite: true,
                }
            },
            {
                breakpoint: 992,
                settings: {
                    slidesToShow: 1,
                    slidesToScroll: 1,
                    arrows: false,
                }
            },
            {
                breakpoint: 767,
                settings: {
                    slidesToShow: 2,
                    slidesToScroll: 1,
                    arrows: false,
                }
            },
            {
                breakpoint: 575,
                settings: {
                    slidesToShow: 1,
                    slidesToScroll: 1,
                    arrows: false,
                }
            },
        ]
    });


    /*=============================================
        =    		Brand Active		      =
    =============================================*/
    $('.brand-active').slick({
        dots: false,
        infinite: true,
        speed: 1000,
        autoplay: true,
        arrows: false,
        slidesToShow: 6,
        slidesToScroll: 2,
        responsive: [{
                breakpoint: 1200,
                settings: {
                    slidesToShow: 5,
                    slidesToScroll: 1,
                    infinite: true,
                }
            },
            {
                breakpoint: 992,
                settings: {
                    slidesToShow: 4,
                    slidesToScroll: 1
                }
            },
            {
                breakpoint: 767,
                settings: {
                    slidesToShow: 3,
                    slidesToScroll: 1,
                    arrows: false,
                }
            },
            {
                breakpoint: 575,
                settings: {
                    slidesToShow: 2,
                    slidesToScroll: 1,
                    arrows: false,
                }
            },
        ]
    });


    /*=============================================
        =    	   Testimonial Active		    =
    =============================================*/
    $('.testimonial-active').slick({
        dots: true,
        infinite: true,
        speed: 1000,
        autoplay: false,
        centerMode: false,
        centerPadding: '0px',
        arrows: false,
        slidesToShow: 3,
        slidesToScroll: 1,
        responsive: [{
                breakpoint: 1200,
                settings: {
                    slidesToShow: 3,
                    slidesToScroll: 1,
                    infinite: true,
                }
            },
            {
                breakpoint: 992,
                settings: {
                    slidesToShow: 2,
                    slidesToScroll: 1
                }
            },
            {
                breakpoint: 767,
                settings: {
                    slidesToShow: 1,
                    slidesToScroll: 1,
                    arrows: false,
                }
            },
            {
                breakpoint: 575,
                settings: {
                    slidesToShow: 1,
                    slidesToScroll: 1,
                    arrows: false,
                }
            },
        ]
    });


    /*=============================================
        =         Sidebar Product Active        =
    =============================================*/
    $('.sidebar-product-active').slick({
        dots: false,
        infinite: true,
        speed: 1000,
        autoplay: false,
        arrows: true,
        slidesToShow: 1,
        slidesToScroll: 1,
        prevArrow: '<span class="slick-prev"><i class="fas fa-angle-left"></i></span>',
        nextArrow: '<span class="slick-next"><i class="fas fa-angle-right"></i></span>',
        appendArrows: ".slider-nav",
        responsive: [{
                breakpoint: 1200,
                settings: {
                    slidesToShow: 1,
                    slidesToScroll: 1,
                    infinite: true,
                }
            },
            {
                breakpoint: 992,
                settings: {
                    slidesToShow: 1,
                    slidesToScroll: 1
                }
            },
            {
                breakpoint: 767,
                settings: {
                    slidesToShow: 1,
                    slidesToScroll: 1,
                }
            },
            {
                breakpoint: 575,
                settings: {
                    slidesToShow: 1,
                    slidesToScroll: 1,
                }
            },
        ]
    });


    /*=============================================
        =         Related Product Active        =
    =============================================*/
    $('.related-product-active').slick({
        dots: false,
        infinite: true,
        speed: 1000,
        autoplay: false,
        rtl: isRtl ? false : true,
        arrows: true,
        slidesToShow: 4,
        slidesToScroll: 1,
        prevArrow: '<span class="slick-prev"><i class="fas fa-angle-left"></i></span>',
        nextArrow: '<span class="slick-next"><i class="fas fa-angle-right"></i></span>',
        appendArrows: ".slider-nav",
        responsive: [{
                breakpoint: 1200,
                settings: {
                    slidesToShow: 3,
                    slidesToScroll: 1,
                    infinite: true,
                }
            },
            {
                breakpoint: 992,
                settings: {
                    slidesToShow: 2,
                    slidesToScroll: 1
                }
            },
            {
                breakpoint: 767,
                settings: {
                    slidesToShow: 2,
                    slidesToScroll: 1,
                }
            },
            {
                breakpoint: 575,
                settings: {
                    slidesToShow: 1,
                    slidesToScroll: 1,
                    arrows: false,
                }
            },
        ]
    });


    /*=============================================
        =         Product Rating Active        =
    =============================================*/
    var options = {
        max_value: 5,
        step_size: 1,
        initial_value: 0,
        selected_symbol_type: 'fontawesome_star', // Must be a key from symbols
        cursor: 'default',
        readonly: false,
        change_once: false, // Determines if the rating can only be set once
        ajax_method: 'POST',
        additional_data: {} // Additional data to send to the server
    }

    /*=============================================
        =    		Magnific Popup		      =
    =============================================*/
    $('.popup-image').magnificPopup({
        type: 'image',
        gallery: {
            enabled: true
        }
    });

    /*=============================================
        =    		Isotope	Active  	      =
    =============================================*/
    // $('.exclusive-active').imagesLoaded(function () {
    //     // init Isotope
    //     var $grid = $('.exclusive-active').isotope({
    //         itemSelector: '.grid-item',
    //         percentPosition: true,
    //         originLeft: isRtl ? false : true,
    //         masonry: {
    //             columnWidth: '.grid-sizer',
    //         }
    //     });
    //     // filter items on button click
    //     $('.product-menu').on('click', 'button', function () {
    //         var filterValue = $(this).attr('data-filter');
    //         $grid.isotope({ filter: filterValue });
    //     });

    // });

    //for menu active class
    $('.product-menu button').on('click', function(event) {
        $(this).siblings('.active').removeClass('active');
        $(this).addClass('active');
        event.preventDefault();
    });




    /*=============================================
        =    	Shop Details Active  	       =
    =============================================*/
    $('.shop-details-active').slick({
        lazyLoad: 'ondemand',
        slidesToShow: 1,
        slidesToScroll: 1,
        arrows: false,
        dots: false,
        fade: true,
        rtl: "rtl" === window.document.querySelector("html").getAttribute("dir"),
        asNavFor: '.shop-details-nav'
    });

    $('.shop-details-nav').slick({
        slidesToShow: 3,
        slidesToScroll: 1,
        asNavFor: '.shop-details-active',
        arrows: false,
        dots: false,
        vertical: true,
        infinite: true,
        rtl: false,
        speed: 10,
        swipe: !0,
        swipeToSlide: false,
        touchMove: !0,
        touchThreshold: 5,
        useCSS: !0,
        useTransform: !0,
        variableWidth: !1,
        waitForAnimate: !0,
        zIndex: 1e3,
        focusOnSelect: true,
        responsive: [{
                breakpoint: 1200,
                settings: {
                    slidesToShow: 3,
                    slidesToScroll: 1,
                    infinite: true,
                    vertical: false,
                }
            },
            {
                breakpoint: 992,
                settings: {
                    slidesToShow: 3,
                    slidesToScroll: 1
                }
            },
            {
                breakpoint: 767,
                settings: {
                    slidesToShow: 3,
                    slidesToScroll: 1,
                    arrows: false,
                }
            },
            {
                breakpoint: 575,
                settings: {
                    slidesToShow: 3,
                    slidesToScroll: 1,
                    arrows: false,
                },
            },
        ]
    });


    /*=============================================
        =    		 Cart Active  	         =
    =============================================*/
    $(".cart-plus-minus").append('<div class="dec qtybutton">-</div><div class="inc qtybutton">+</div>');


    $(".qtybutton").on("click", function() {



        const skipCart = $(".cart-plus-minus").attr('data-skipCart');
        const changePrice = $(".cart-plus-minus").attr('data-changePrice');

        var $button = $(this);
        var oldValue = $button.parent().find("input").val();
        if ($button.text() == "+") {
            var newVal = parseFloat(oldValue) + 1;
        } else {
            // Don't allow decrementing below zero
            if (oldValue > 1) {
                var newVal = parseFloat(oldValue) - 1;
            } else {
                newVal = 1;
            }
        }


        $button.parent().find("input").val(newVal);


        var id = $button.parent().find("input").data('id');

        if (!skipCart) {
            let price = $button.closest('tr').data('price');
            price = formatCurrency((parseFloat(price) * newVal).toFixed(2));
            $button.closest('tr').find('.subtotal-price').text(price);
            let totalCartPrice = 0;
            $('.subtotal-price').each(function() {
                totalCartPrice += parseFloat($(this).text().replace(/[^0-9.-]+/g, ""));
            });
            totalCartPrice = formatCurrency(totalCartPrice.toFixed(2));
            $(".cart-price").not(".nav-item .cart-price").html(totalCartPrice), newVal > 0 ? $(".cart-total-price").show() : $(".cart-total-price").hide();

            // update cart offacanvas items
            $(`.qty-box[data-id="${id}"]`).html(newVal);
            $(`.qty-box[data-id="${id}"]`).parent().parent().find(".value").text(price);
        }
        if (changePrice) {
            let ourform = $(this).closest('.buynow-class');
            updatePrice(1, ourform);
        }

    });

    /*=============================================
        =      Newsletter Modal Active  	     =
    =============================================*/
    function popupModal() {

        if (!$(".newsletter-popup").length)
            return;

        setTimeout(function() {
            $('#exampleModal').modal('show');
        }, 5000);
    }

})(jQuery);

/*=============================================
        =         New Header             =
=============================================*/
$(document).ready(function() {

    $(".resp-toggle").click(function(event) {
        event.preventDefault();
        $(".side-responsive-nav").toggleClass("active");
        $("body").toggleClass("overlay-active");
    });

    // Hide cart summary when clicking outside of it
    $(document).click(function(event) {
        var $target = $(event.target);

        // Check if the clicked element is not inside the cart summary and not the cart toggle button
        if (!$target.closest(".side-responsive-nav").length && !$target.closest(".resp-toggle").length) {
            $(".side-responsive-nav").removeClass("active");
            $("body").removeClass("overlay-active");
        }
    });


    $(".cart-toggle").click(function(event) {
        event.preventDefault();
        $(".side-cart-summary").toggleClass("active");
    });

    $(".search-toggle").click(function(event) {
        event.preventDefault();
        $(".search-wrapper").toggleClass("active");
    });

    // Hide cart summary when clicking outside of it
    $(document).click(function(event) {
        var $target = $(event.target);

        // Check if the clicked element is not inside the cart summary and not the cart toggle button
        if (!$target.closest(".side-cart-summary").length && !$target.closest(".cart-toggle").length) {
            $(".side-cart-summary").removeClass("active");
        }
    });


    $('.toggle-child-menu').click(function(e) {
        e.preventDefault();
        $(this).siblings('.resp-nav-child').slideToggle('fast');
        $(this).siblings('.resp-nav-arrow').find('i').toggleClass('fa-angle-right fa-angle-down');
    });

});



window.onload = function() {

    const dropifyOffers = {
        initActive: function() {
            const els = document.querySelectorAll('[ref="drop-xcvr"]');
            els.forEach(function(el) {
                if (el.checked) {
                    el.closest('[ref="drop-ghty"]').classList.add('active');
                }
            });
        },
        initClickEvent: function() {
            const items = document.querySelectorAll('[ref="drop-ghty"]');
            for (var i = 0; i < items.length; i++) {
                items[i].addEventListener('click', function(event) {
                    dropifyOffers.removeActive();
                    dropifyOffers.addActive(this);
                    dropifyOffers.addChecked(this);
                });
            }
        },
        removeActive: function() {
            const elements = document.querySelectorAll('[ref="drop-ghty"]');
            elements.forEach(function(el) {
                el.classList.remove('active');
            });
        },
        addActive: function(hadi) {
            const item = hadi.closest('[ref="drop-ghty"]');
            item.classList.add('active');
        },
        addChecked: function(hadi) {
            const radio = hadi.querySelector('[ref="drop-xcvr"]');
            radio.checked = true;
        }
    };

    dropifyOffers.initActive();
    dropifyOffers.initClickEvent();
};


$(document).ready(function() {
    const $localisationLink = $('[ref="localisation-link"]'),
        $fr345 = $('.fr-345');

    if ($localisationLink.length > 0) {
        $localisationLink.on('click', function(e) {
            e.preventDefault();
        });
    }

    if ($fr345.length > 0) {
        $fr345.click(function() {
            var content = $(this).next(".fr-132");
            content.toggleClass("show");
            $(this).find('i').toggleClass('rotate');
        });
    }
});

function formatNumber(price) {
    let number = parseFloat(price);
    return number.toLocaleString('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    });
}

function continueShoping() {
    window.location.replace("/categories");
}


$(function() {
    if ($('[ref="newsletter-form"]').length) {
        $('[ref="newsletter-form"]').on('submit', function(event) {
            event.preventDefault();
            const $submitButton = $(this).find('button[type="submit"]');
            let csrfToken = $('meta[name="csrf-token"]').attr('content'),
                email = $(this).find('input[name="email"]').val(),
                buttonText = $submitButton.text(),
                msg = $(this).data('msg');
            $submitButton.prop('disabled', true);
            $submitButton.html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>');

            if (!email.trim()) {
                Swal.fire({
                    icon: 'error',
                    title: 'Oops...',
                    text: msg,
                });
                $submitButton.html(buttonText);
                $submitButton.prop('disabled', false);
                return false;
            } else {
                $.ajax({
                    url: base_url + "newsletter",
                    type: 'POST',
                    data: {
                        email: email,
                        _token: csrfToken
                    },
                    success: function(response) {
                        Swal.fire({
                            icon: 'success',
                            // title: 'Success',
                            text: thank_you_newletter,
                            confirmButtonText: ok_message,
                            customClass: {
                                confirmButton: 'btn text-bg-success w-100'
                            }
                        });
                        $submitButton.html(buttonText);
                        $submitButton.prop('disabled', false);
                        $('#exampleModal').modal('hide');
                        document.cookie = "dont_show_promo=true; path=/; max-age=" + 30 * 24 * 60 * 60; // 30 days
                    },
                    error: function(response) {
                        Swal.fire({
                            icon: 'error',
                            title: 'Oops...',
                            text: response.message,
                        });
                        $submitButton.html(buttonText);
                        $submitButton.prop('disabled', false);
                    }
                });
            }
        });
    }

    $("#comment-check").on("change", function() {
        let isChecked = $(this).is(":checked");
        document.cookie = isChecked ?
            "dont_show_promo=yes; expires=Fri, 31 Dec 9999 23:59:59 UTC; path=/" :
            "dont_show_promo=yes; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/";
    });

    $("#_saveNewsLetter_form").submit(function(e) {
        e.preventDefault(),
            $.ajax({
                type: "post",
                dataType: "html",
                url: base_url + "ajax/saveNewsLetter_email",
                data: {
                    _saveNewsLetter_email: $("input[name=_saveNewsLetter_email]").val(),
                },
                success: function(e) {
                    $("div#exampleModal").modal("hide");
                },
            });
    });

    $("#_saveNewsLetter_form3").submit(function(e) {
        e.preventDefault();

        let email = $("input[name=_saveNewsLetter_email3]").val();

        if (email == "" || !email.length) {
            $("#_saveNewsLetter_form3_err") ? .show();
            return;
        }

        $("#_saveNewsLetter_form3_err") ? .hide();

        $.ajax({
            url: base_url + "newsletter",
            type: 'POST',
            data: {
                email: email,
                _token: $('meta[name="csrf-token"]').attr('content')
            },
            success: function(e) {
                $("input[name=_saveNewsLetter_email3]").val("");
                Swal.fire({
                    title: thank_you_newletter,
                    icon: 'success',
                    type: 'success',
                    buttonsStyling: !1,
                    confirmButtonClass: "btn w-100 text-white btn-success"

                });
            }
        });
    });

});