/*!
 * Bootstrap  v5.3.3 (https://getbootstrap.com/)
 * Copyright 2011-2024 The Bootstrap Authors
 * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)
 */

:root {
    --bs-blue: #3b82f6;
    --bs-indigo: #6366f1;
    --bs-purple: #a855f7;
    --bs-pink: #d63384;
    --bs-red: #ef4444;
    --bs-orange: #f97316;
    --bs-yellow: #eab308;
    --bs-green: #22c05c;
    --bs-teal: #14b8a6;
    --bs-cyan: #06b6d4;
    --bs-black: #000;
    --bs-white: #fff;
    --bs-gray: #71717a;
    --bs-gray-dark: #27272a;
    --bs-gray-100: #fafafa;
    --bs-gray-200: #f4f4f5;
    --bs-gray-300: #e4e4e7;
    --bs-gray-400: #d4d4d8;
    --bs-gray-500: #a1a1aa;
    --bs-gray-600: #71717a;
    --bs-gray-700: #3f3f46;
    --bs-gray-800: #27272a;
    --bs-gray-900: #09090b;
    --bs-primary: #dba87f;
    --bs-secondary: #ebebeb;
    --bs-success: #22c05c;
    --bs-info: #06b6d4;
    --bs-warning: #eab308;
    --bs-danger: #ef4444;
    --bs-light: #fafafa;
    --bs-dark: #09090b;
    --bs-primary-rgb: 219, 168, 127;
    --bs-secondary-rgb: 235, 235, 235;
    --bs-success-rgb: 34, 192, 92;
    --bs-info-rgb: 6, 182, 212;
    --bs-warning-rgb: 234, 179, 8;
    --bs-danger-rgb: 239, 68, 68;
    --bs-light-rgb: 250, 250, 250;
    --bs-dark-rgb: 9, 9, 11;
    --bs-primary-text-emphasis: #584333;
    --bs-secondary-text-emphasis: #5e5e5e;
    --bs-success-text-emphasis: #0e4d25;
    --bs-info-text-emphasis: #024955;
    --bs-warning-text-emphasis: #5e4803;
    --bs-danger-text-emphasis: #601b1b;
    --bs-light-text-emphasis: #3f3f46;
    --bs-dark-text-emphasis: #3f3f46;
    --bs-primary-bg-subtle: #f8eee5;
    --bs-secondary-bg-subtle: #fbfbfb;
    --bs-success-bg-subtle: #d3f2de;
    --bs-info-bg-subtle: #cdf0f6;
    --bs-warning-bg-subtle: #fbf0ce;
    --bs-danger-bg-subtle: #fcdada;
    --bs-light-bg-subtle: #fdfdfd;
    --bs-dark-bg-subtle: #d4d4d8;
    --bs-primary-border-subtle: #f1dccc;
    --bs-secondary-border-subtle: #f7f7f7;
    --bs-success-border-subtle: #a7e6be;
    --bs-info-border-subtle: #9be2ee;
    --bs-warning-border-subtle: #f7e19c;
    --bs-danger-border-subtle: #f9b4b4;
    --bs-light-border-subtle: #f4f4f5;
    --bs-dark-border-subtle: #a1a1aa;
    --bs-white-rgb: 255, 255, 255;
    --bs-black-rgb: 0, 0, 0;
    --bs-font-sans-serif: system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", "Noto Sans", "Liberation Sans", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    --bs-font-monospace: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
    --bs-gradient: linear-gradient(180deg, hsla(0, 0%, 100%, .15), hsla(0, 0%, 100%, 0));
    --bs-body-font-family: var(--bs-font-sans-serif);
    --bs-body-font-size: 0.95rem;
    --bs-body-font-weight: 400;
    --bs-body-line-height: 1.5;
    --bs-body-color: #27272a;
    --bs-body-color-rgb: 39, 39, 42;
    --bs-body-bg: #fff;
    --bs-body-bg-rgb: 255, 255, 255;
    --bs-emphasis-color: #000;
    --bs-emphasis-color-rgb: 0, 0, 0;
    --bs-secondary-color: rgba(39, 39, 42, .75);
    --bs-secondary-color-rgb: 39, 39, 42;
    --bs-secondary-bg: #f4f4f5;
    --bs-secondary-bg-rgb: 244, 244, 245;
    --bs-tertiary-color: rgba(39, 39, 42, .5);
    --bs-tertiary-color-rgb: 39, 39, 42;
    --bs-tertiary-bg: #fafafa;
    --bs-tertiary-bg-rgb: 250, 250, 250;
    --bs-heading-color: inherit;
    --bs-link-color: #27272a;
    --bs-link-color-rgb: 39, 39, 42;
    --bs-link-decoration: underline;
    --bs-link-hover-color: #1f1f22;
    --bs-link-hover-color-rgb: 31, 31, 34;
    --bs-code-color: #d63384;
    --bs-highlight-color: #27272a;
    --bs-highlight-bg: #fbf0ce;
    --bs-border-width: 1px;
    --bs-border-style: solid;
    --bs-border-color: #f4f4f5;
    --bs-border-color-translucent: #f4f4f5;
    --bs-border-radius: 0.5rem;
    --bs-border-radius-sm: 0.025rem;
    --bs-border-radius-lg: 0.75rem;
    --bs-border-radius-xl: 1rem;
    --bs-border-radius-xxl: 2rem;
    --bs-border-radius-2xl: var(--bs-border-radius-xxl);
    --bs-border-radius-pill: 50rem;
    --bs-box-shadow: 0 0.5rem 1rem rgba(63, 63, 70, .075);
    --bs-box-shadow-sm: 0 0.125rem 0.25rem rgba(63, 63, 70, .05);
    --bs-box-shadow-lg: 0 1rem 3rem rgba(63, 63, 70, .075);
    --bs-box-shadow-inset: inset 0 1px 2px rgba(0, 0, 0, .075);
    --bs-focus-ring-width: 0.25rem;
    --bs-focus-ring-opacity: 0.25;
    --bs-focus-ring-color: hsla(27, 56%, 68%, .25);
    --bs-form-valid-color: #22c05c;
    --bs-form-valid-border-color: #22c05c;
    --bs-form-invalid-color: #ef4444;
    --bs-form-invalid-border-color: #ef4444
}

*,
:after,
:before {
    box-sizing: border-box
}

@media(prefers-reduced-motion:no-preference) {
    :root {
        scroll-behavior: smooth
    }
}

body {
    background-color: var(--bs-body-bg);
    color: var(--bs-body-color);
    font-family: var(--bs-body-font-family);
    font-size: var(--bs-body-font-size);
    font-weight: var(--bs-body-font-weight);
    line-height: var(--bs-body-line-height);
    margin: 0;
    text-align: var(--bs-body-text-align);
    -webkit-text-size-adjust: 100%;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0)
}

.h1,
.h2,
.h3,
.h4,
.h5,
.h6,
h1,
h2,
h3,
h4,
h5,
h6 {
    color: var(--bs-heading-color);
    font-weight: 500;
    line-height: 1.2;
    margin-bottom: .5rem;
    margin-top: 0
}

.h1,
h1 {
    font-size: calc(1.3625rem + 1.35vw)
}

@media(min-width:1200px) {
    .h1,
    h1 {
        font-size: 2.375rem
    }
}

.h2,
h2 {
    font-size: calc(1.315rem + .78vw)
}

@media(min-width:1200px) {
    .h2,
    h2 {
        font-size: 1.9rem
    }
}

.h3,
h3 {
    font-size: calc(1.29125rem + .495vw)
}

@media(min-width:1200px) {
    .h3,
    h3 {
        font-size: 1.6625rem
    }
}

.h4,
h4 {
    font-size: calc(1.2675rem + .21vw)
}

@media(min-width:1200px) {
    .h4,
    h4 {
        font-size: 1.425rem
    }
}

.h5,
h5 {
    font-size: 1.1875rem
}

.h6,
h6 {
    font-size: .95rem
}

p {
    margin-bottom: 1rem;
    margin-top: 0
}

address {
    font-style: normal;
    line-height: inherit;
    margin-bottom: 1rem
}

ol,
ul {
    padding-right: 2rem
}

dl,
ol,
ul {
    margin-bottom: 1rem;
    margin-top: 0
}

ol ol,
ol ul,
ul ol,
ul ul {
    margin-bottom: 0
}

dt {
    font-weight: 700
}

blockquote {
    margin: 0 0 1rem
}

b,
strong {
    font-weight: bolder
}

.small,
small {
    font-size: .875em
}

a {
    color: rgba(var(--bs-link-color-rgb), var(--bs-link-opacity, 1));
    text-decoration: underline
}

a:hover {
    --bs-link-color-rgb: var(--bs-link-hover-color-rgb)
}

a:not([href]):not([class]),
a:not([href]):not([class]):hover {
    color: inherit;
    text-decoration: none
}

code {
    font-family: var(--bs-font-monospace);
    font-size: 1em
}

code {
    color: var(--bs-code-color);
    font-size: .875em;
    word-wrap: break-word
}

a>code {
    color: inherit
}

figure {
    margin: 0 0 1rem
}

img,
svg {
    vertical-align: middle
}

table {
    border-collapse: collapse;
    caption-side: bottom
}

th {
    text-align: inherit;
    text-align: -webkit-match-parent
}

tbody,
td,
th,
thead,
tr {
    border: 0 solid;
    border-color: inherit
}

label {
    display: inline-block
}

button {
    border-radius: 0
}

button:focus:not(:focus-visible) {
    outline: 0
}

button,
input,
optgroup,
select,
textarea {
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
    margin: 0
}

button,
select {
    text-transform: none
}

[role=button] {
    cursor: pointer
}

select {
    word-wrap: normal
}

select:disabled {
    opacity: 1
}

[type=button],
[type=reset],
[type=submit],
button {
    -webkit-appearance: button
}

[type=button]:not(:disabled),
[type=reset]:not(:disabled),
[type=submit]:not(:disabled),
button:not(:disabled) {
    cursor: pointer
}

::-moz-focus-inner {
    border-style: none;
    padding: 0
}

textarea {
    resize: vertical
}

::-webkit-datetime-edit-day-field,
::-webkit-datetime-edit-fields-wrapper,
::-webkit-datetime-edit-hour-field,
::-webkit-datetime-edit-minute,
::-webkit-datetime-edit-month-field,
::-webkit-datetime-edit-text,
::-webkit-datetime-edit-year-field {
    padding: 0
}

::-webkit-inner-spin-button {
    height: auto
}

[type=search] {
    -webkit-appearance: textfield;
    outline-offset: -2px
}

::-webkit-search-decoration {
    -webkit-appearance: none
}

::-webkit-color-swatch-wrapper {
    padding: 0
}

::file-selector-button {
    -webkit-appearance: button;
    font: inherit
}

output {
    display: inline-block
}

iframe {
    border: 0
}

summary {
    cursor: pointer;
    display: list-item
}

progress {
    vertical-align: baseline
}

[hidden] {
    display: none !important
}

.display-4 {
    font-size: calc(1.475rem + 2.7vw);
    font-weight: 300;
    line-height: 1.2
}

@media(min-width:1200px) {
    .display-4 {
        font-size: 3.5rem
    }
}

.display-5 {
    font-size: calc(1.425rem + 2.1vw);
    font-weight: 300;
    line-height: 1.2
}

@media(min-width:1200px) {
    .display-5 {
        font-size: 3rem
    }
}

.display-6 {
    font-size: calc(1.375rem + 1.5vw);
    font-weight: 300;
    line-height: 1.2
}

@media(min-width:1200px) {
    .display-6 {
        font-size: 2.5rem
    }
}

.blockquote {
    font-size: 1.1875rem;
    margin-bottom: 1rem
}

.blockquote>:last-child {
    margin-bottom: 0
}

.img-fluid {
    height: auto;
    max-width: 100%
}

.figure {
    display: inline-block
}

.container {
    --bs-gutter-x: 1.5rem;
    --bs-gutter-y: 0;
    margin-right: auto;
    margin-left: auto;
    padding-right: calc(var(--bs-gutter-x)*.5);
    padding-left: calc(var(--bs-gutter-x)*.5);
    width: 100%
}

@media(min-width:576px) {
    .container {
        max-width: 540px
    }
}

@media(min-width:768px) {
    .container {
        max-width: 720px
    }
}

@media(min-width:992px) {
    .container {
        max-width: 960px
    }
}

@media(min-width:1200px) {
    .container {
        max-width: 1140px
    }
}

@media(min-width:1400px) {
    .container {
        max-width: 1320px
    }
}

:root {
    --bs-breakpoint-xs: 0;
    --bs-breakpoint-sm: 576px;
    --bs-breakpoint-md: 768px;
    --bs-breakpoint-lg: 992px;
    --bs-breakpoint-xl: 1200px;
    --bs-breakpoint-xxl: 1400px
}

.row {
    --bs-gutter-x: 1.5rem;
    --bs-gutter-y: 0;
    display: flex;
    flex-wrap: wrap;
    margin-right: calc(var(--bs-gutter-x)*-.5);
    margin-left: calc(var(--bs-gutter-x)*-.5);
    margin-top: calc(var(--bs-gutter-y)*-1)
}

.row>* {
    flex-shrink: 0;
    margin-top: var(--bs-gutter-y);
    max-width: 100%;
    padding-right: calc(var(--bs-gutter-x)*.5);
    padding-left: calc(var(--bs-gutter-x)*.5);
    width: 100%
}

.col {
    flex: 1 0 0%
}

.col-3 {
    flex: 0 0 auto;
    width: 25%
}

.col-7 {
    flex: 0 0 auto;
    width: 58.33333333%
}

.col-12 {
    flex: 0 0 auto;
    width: 100%
}

.g-0 {
    --bs-gutter-x: 0
}

.g-0 {
    --bs-gutter-y: 0
}

.g-3 {
    --bs-gutter-x: 1rem
}

.g-3 {
    --bs-gutter-y: 1rem
}

.g-4 {
    --bs-gutter-x: 1.5rem
}

.g-4 {
    --bs-gutter-y: 1.5rem
}

.g-5 {
    --bs-gutter-x: 3rem
}

.g-5 {
    --bs-gutter-y: 3rem
}

@media(min-width:576px) {
    .col-sm-3 {
        flex: 0 0 auto;
        width: 25%
    }
    .col-sm-6 {
        flex: 0 0 auto;
        width: 50%
    }
    .col-sm-8 {
        flex: 0 0 auto;
        width: 66.66666667%
    }
    .col-sm-12 {
        flex: 0 0 auto;
        width: 100%
    }
}

@media(min-width:768px) {
    .col-md-4 {
        flex: 0 0 auto;
        width: 33.33333333%
    }
    .col-md-6 {
        flex: 0 0 auto;
        width: 50%
    }
    .col-md-8 {
        flex: 0 0 auto;
        width: 66.66666667%
    }
    .col-md-10 {
        flex: 0 0 auto;
        width: 83.33333333%
    }
    .col-md-11 {
        flex: 0 0 auto;
        width: 91.66666667%
    }
    .col-md-12 {
        flex: 0 0 auto;
        width: 100%
    }
    .g-md-4 {
        --bs-gutter-x: 1.5rem
    }
    .g-md-4 {
        --bs-gutter-y: 1.5rem
    }
    .g-md-5 {
        --bs-gutter-x: 3rem
    }
    .g-md-5 {
        --bs-gutter-y: 3rem
    }
}

@media(min-width:992px) {
    .col-lg-3 {
        flex: 0 0 auto;
        width: 25%
    }
    .col-lg-4 {
        flex: 0 0 auto;
        width: 33.33333333%
    }
    .col-lg-5 {
        flex: 0 0 auto;
        width: 41.66666667%
    }
    .col-lg-6 {
        flex: 0 0 auto;
        width: 50%
    }
    .col-lg-7 {
        flex: 0 0 auto;
        width: 58.33333333%
    }
    .col-lg-8 {
        flex: 0 0 auto;
        width: 66.66666667%
    }
    .col-lg-11 {
        flex: 0 0 auto;
        width: 91.66666667%
    }
    .col-lg-12 {
        flex: 0 0 auto;
        width: 100%
    }
    .g-lg-3 {
        --bs-gutter-x: 1rem
    }
    .g-lg-3 {
        --bs-gutter-y: 1rem
    }
    .g-lg-5 {
        --bs-gutter-x: 3rem
    }
    .g-lg-5 {
        --bs-gutter-y: 3rem
    }
}

@media(min-width:1200px) {
    .col-xl-2 {
        flex: 0 0 auto;
        width: 16.66666667%
    }
    .col-xl-3 {
        flex: 0 0 auto;
        width: 25%
    }
    .col-xl-4 {
        flex: 0 0 auto;
        width: 33.33333333%
    }
    .col-xl-6 {
        flex: 0 0 auto;
        width: 50%
    }
    .col-xl-9 {
        flex: 0 0 auto;
        width: 75%
    }
    .col-xl-12 {
        flex: 0 0 auto;
        width: 100%
    }
}

.table {
    --bs-table-color-type: initial;
    --bs-table-bg-type: initial;
    --bs-table-color-state: initial;
    --bs-table-bg-state: initial;
    --bs-table-color: var(--bs-emphasis-color);
    --bs-table-bg: var(--bs-body-bg);
    --bs-table-border-color: var(--bs-border-color);
    --bs-table-accent-bg: transparent;
    --bs-table-striped-color: var(--bs-emphasis-color);
    --bs-table-striped-bg: rgba(var(--bs-emphasis-color-rgb), 0.05);
    --bs-table-active-color: var(--bs-emphasis-color);
    --bs-table-active-bg: rgba(var(--bs-emphasis-color-rgb), 0.1);
    --bs-table-hover-color: var(--bs-emphasis-color);
    --bs-table-hover-bg: rgba(var(--bs-emphasis-color-rgb), 0.075);
    border-color: var(--bs-table-border-color);
    margin-bottom: 1rem;
    vertical-align: top;
    width: 100%
}

.table>:not(caption)>*>* {
    background-color: var(--bs-table-bg);
    border-bottom-width: var(--bs-border-width);
    box-shadow: inset 0 0 0 9999px var(--bs-table-bg-state, var(--bs-table-bg-type, var(--bs-table-accent-bg)));
    color: var(--bs-table-color-state, var(--bs-table-color-type, var(--bs-table-color)));
    padding: .5rem
}

.table>tbody {
    vertical-align: inherit
}

.table>thead {
    vertical-align: bottom
}

.table-borderless>:not(caption)>*>* {
    border-bottom-width: 0
}

.table-borderless>:not(:first-child) {
    border-top-width: 0
}

@media(max-width:1199.98px) {
    .table-responsive-xl {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch
    }
}

.form-label {
    margin-bottom: .5rem
}

.form-control {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-clip: padding-box;
    background-color: var(--bs-body-bg);
    border: var(--bs-border-width) solid var(--bs-border-color);
    border-radius: var(--bs-border-radius);
    color: var(--bs-body-color);
    display: block;
    font-size: .925rem;
    font-weight: 400;
    line-height: 1.5;
    padding: .7rem .75rem;
    transition: border-color .15s ease-in-out, box-shadow .15s ease-in-out;
    width: 100%
}

@media(prefers-reduced-motion:reduce) {
    .form-control {
        transition: none
    }
}

.form-control[type=file] {
    overflow: hidden
}

.form-control[type=file]:not(:disabled):not([readonly]) {
    cursor: pointer
}

.form-control:focus {
    background-color: var(--bs-body-bg);
    border-color: #edd4bf;
    box-shadow: none;
    color: var(--bs-body-color);
    outline: 0
}

.form-control::-webkit-date-and-time-value {
    height: 1.5em;
    margin: 0;
    min-width: 85px
}

.form-control::-webkit-datetime-edit {
    display: block;
    padding: 0
}

.form-control::-moz-placeholder {
    color: var(--bs-secondary-color);
    opacity: 1
}

.form-control::placeholder {
    color: var(--bs-secondary-color);
    opacity: 1
}

.form-control:disabled {
    background-color: var(--bs-secondary-bg);
    opacity: 1
}

.form-control::file-selector-button {
    background-color: var(--bs-tertiary-bg);
    border: 0 solid;
    border-color: inherit;
    border-inline-end-width: var(--bs-border-width);
    border-radius: 0;
    color: var(--bs-body-color);
    margin: -.7rem -.75rem;
    margin-inline-end: .75rem;
    padding: .7rem .75rem;
    pointer-events: none;
    transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out
}

@media(prefers-reduced-motion:reduce) {
    .form-control::file-selector-button {
        transition: none
    }
}

.form-control:hover:not(:disabled):not([readonly])::file-selector-button {
    background-color: var(--bs-secondary-bg)
}

textarea.form-control {
    min-height: calc(1.5em + 1.4rem + var(--bs-border-width)*2)
}

.form-select {
    --bs-form-select-bg-img: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3E%3Cpath fill='none' stroke='%2327272a' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3E%3C/svg%3E");
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-color: var(--bs-body-bg);
    background-image: var(--bs-form-select-bg-img), var(--bs-form-select-bg-icon, none);
    background-position: left .75rem center;
    background-repeat: no-repeat;
    background-size: 16px 12px;
    border: var(--bs-border-width) solid var(--bs-border-color);
    border-radius: var(--bs-border-radius);
    color: var(--bs-body-color);
    display: block;
    font-size: .925rem;
    font-weight: 400;
    line-height: 1.5;
    padding: .7rem .75rem .7rem 2.25rem;
    transition: border-color .15s ease-in-out, box-shadow .15s ease-in-out;
    width: 100%
}

@media(prefers-reduced-motion:reduce) {
    .form-select {
        transition: none
    }
}

.form-select:focus {
    border-color: #edd4bf;
    box-shadow: 0 0 0 .25rem hsla(27, 56%, 68%, .25);
    outline: 0
}

.form-select[multiple],
.form-select[size]:not([size="1"]) {
    background-image: none;
    padding-left: .75rem
}

.form-select:disabled {
    background-color: var(--bs-secondary-bg)
}

.form-select:-moz-focusring {
    color: transparent;
    text-shadow: 0 0 0 var(--bs-body-color)
}

.form-check-input {
    --bs-form-check-bg: var(--bs-body-bg);
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-color: var(--bs-form-check-bg);
    background-image: var(--bs-form-check-bg-image);
    background-position: 50%;
    background-repeat: no-repeat;
    background-size: contain;
    border: var(--bs-border-width) solid var(--bs-border-color);
    flex-shrink: 0;
    height: 1em;
    margin-top: .25em;
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
    vertical-align: top;
    width: 1em
}

.form-check-input[type=checkbox] {
    border-radius: .25em
}

.form-check-input[type=radio] {
    border-radius: 50%
}

.form-check-input:active {
    filter: brightness(90%)
}

.form-check-input:focus {
    border-color: #edd4bf;
    box-shadow: 0 0 0 .25rem hsla(27, 56%, 68%, .25);
    outline: 0
}

.form-check-input:checked {
    background-color: #dba87f;
    border-color: #dba87f
}

.form-check-input:checked[type=checkbox] {
    --bs-form-check-bg-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3E%3Cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='m6 10 3 3 6-6'/%3E%3C/svg%3E")
}

.form-check-input:checked[type=radio] {
    --bs-form-check-bg-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='2' fill='%23fff'/%3E%3C/svg%3E")
}

.form-check-input[type=checkbox]:indeterminate {
    background-color: #dba87f;
    border-color: #dba87f;
    --bs-form-check-bg-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3E%3Cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10h8'/%3E%3C/svg%3E")
}

.form-check-input:disabled {
    filter: none;
    opacity: .5;
    pointer-events: none
}

.form-check-input:disabled~.form-check-label,
.form-check-input[disabled]~.form-check-label {
    cursor: default;
    opacity: .5
}

.btn-check {
    position: absolute;
    clip: rect(0, 0, 0, 0);
    pointer-events: none
}

.btn-check:disabled+.btn,
.btn-check[disabled]+.btn {
    filter: none;
    opacity: .65;
    pointer-events: none
}

.input-group {
    align-items: stretch;
    display: flex;
    flex-wrap: wrap;
    position: relative;
    width: 100%
}

.input-group>.form-control,
.input-group>.form-select {
    flex: 1 1 auto;
    min-width: 0;
    position: relative;
    width: 1%
}

.input-group>.form-control:focus,
.input-group>.form-select:focus {
    z-index: 5
}

.input-group .btn {
    position: relative;
    z-index: 2
}

.input-group .btn:focus {
    z-index: 5
}

.input-group:not(.has-validation)>.dropdown-toggle:nth-last-child(n+3),
.input-group:not(.has-validation)>:not(:last-child):not(.dropdown-toggle):not(.dropdown-menu):not(.form-floating) {
    border-bottom-left-radius: 0;
    border-top-left-radius: 0
}

.input-group>:not(:first-child):not(.dropdown-menu):not(.valid-tooltip):not(.valid-feedback):not(.invalid-tooltip):not(.invalid-feedback) {
    border-bottom-right-radius: 0;
    border-top-right-radius: 0;
    margin-right: calc(var(--bs-border-width)*-1)
}

.form-control.is-invalid {
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='none' stroke='%23ef4444' viewBox='0 0 12 12'%3E%3Ccircle cx='6' cy='6' r='4.5'/%3E%3Cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3E%3Ccircle cx='6' cy='8.2' r='.6' fill='%23ef4444' stroke='none'/%3E%3C/svg%3E");
    background-position: left calc(.375em + .35rem) center;
    background-repeat: no-repeat;
    background-size: calc(.75em + .7rem) calc(.75em + .7rem);
    border-color: var(--bs-form-invalid-border-color);
    padding-left: calc(1.5em + 1.4rem)
}

.form-control.is-invalid:focus {
    border-color: var(--bs-form-invalid-border-color);
    box-shadow: 0 0 0 .25rem rgba(var(--bs-danger-rgb), .25)
}

textarea.form-control.is-invalid {
    background-position: top calc(.375em + .35rem) left calc(.375em + .35rem);
    padding-left: calc(1.5em + 1.4rem)
}

.form-select.is-invalid {
    border-color: var(--bs-form-invalid-border-color)
}

.form-select.is-invalid:not([multiple]):not([size]),
.form-select.is-invalid:not([multiple])[size="1"] {
    --bs-form-select-bg-icon: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='none' stroke='%23ef4444' viewBox='0 0 12 12'%3E%3Ccircle cx='6' cy='6' r='4.5'/%3E%3Cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3E%3Ccircle cx='6' cy='8.2' r='.6' fill='%23ef4444' stroke='none'/%3E%3C/svg%3E");
    background-position: left .75rem center, center left 2.25rem;
    background-size: 16px 12px, calc(.75em + .7rem) calc(.75em + .7rem);
    padding-left: 4.125rem
}

.form-select.is-invalid:focus {
    border-color: var(--bs-form-invalid-border-color);
    box-shadow: 0 0 0 .25rem rgba(var(--bs-danger-rgb), .25)
}

.form-check-input.is-invalid {
    border-color: var(--bs-form-invalid-border-color)
}

.form-check-input.is-invalid:checked {
    background-color: var(--bs-form-invalid-color)
}

.form-check-input.is-invalid:focus {
    box-shadow: 0 0 0 .25rem rgba(var(--bs-danger-rgb), .25)
}

.form-check-input.is-invalid~.form-check-label {
    color: var(--bs-form-invalid-color)
}

.input-group>.form-control:not(:focus).is-invalid,
.input-group>.form-select:not(:focus).is-invalid {
    z-index: 4
}

.btn {
    --bs-btn-padding-x: 0.75rem;
    --bs-btn-padding-y: 0.7rem;
    --bs-btn-font-family: ;
    --bs-btn-font-size: 0.925rem;
    --bs-btn-font-weight: 400;
    --bs-btn-line-height: 1.5;
    --bs-btn-color: var(--bs-body-color);
    --bs-btn-bg: transparent;
    --bs-btn-border-width: var(--bs-border-width);
    --bs-btn-border-color: transparent;
    --bs-btn-border-radius: var(--bs-border-radius);
    --bs-btn-hover-border-color: transparent;
    --bs-btn-box-shadow: inset 0 1px 0 hsla(0, 0%, 100%, .15), 0 1px 1px rgba(0, 0, 0, .075);
    --bs-btn-disabled-opacity: 0.65;
    --bs-btn-focus-box-shadow: 0 0 0 0.25rem rgba(var(--bs-btn-focus-shadow-rgb), .5);
    background-color: var(--bs-btn-bg);
    border: var(--bs-btn-border-width) solid var(--bs-btn-border-color);
    border-radius: var(--bs-btn-border-radius);
    color: var(--bs-btn-color);
    cursor: pointer;
    display: inline-block;
    font-family: var(--bs-btn-font-family);
    font-size: var(--bs-btn-font-size);
    font-weight: var(--bs-btn-font-weight);
    line-height: var(--bs-btn-line-height);
    padding: var(--bs-btn-padding-y) var(--bs-btn-padding-x);
    text-align: center;
    text-decoration: none;
    transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    vertical-align: middle
}

@media(prefers-reduced-motion:reduce) {
    .btn {
        transition: none
    }
}

.btn:hover {
    background-color: var(--bs-btn-hover-bg);
    border-color: var(--bs-btn-hover-border-color);
    color: var(--bs-btn-hover-color)
}

.btn-check+.btn:hover {
    background-color: var(--bs-btn-bg);
    border-color: var(--bs-btn-border-color);
    color: var(--bs-btn-color)
}

.btn:focus-visible {
    background-color: var(--bs-btn-hover-bg);
    border-color: var(--bs-btn-hover-border-color);
    box-shadow: var(--bs-btn-focus-box-shadow);
    color: var(--bs-btn-hover-color);
    outline: 0
}

.btn-check:focus-visible+.btn {
    border-color: var(--bs-btn-hover-border-color);
    box-shadow: var(--bs-btn-focus-box-shadow);
    outline: 0
}

.btn-check:checked+.btn,
.btn.active,
.btn.show,
.btn:first-child:active,
:not(.btn-check)+.btn:active {
    background-color: var(--bs-btn-active-bg);
    border-color: var(--bs-btn-active-border-color);
    color: var(--bs-btn-active-color)
}

.btn-check:checked+.btn:focus-visible,
.btn.active:focus-visible,
.btn.show:focus-visible,
.btn:first-child:active:focus-visible,
:not(.btn-check)+.btn:active:focus-visible {
    box-shadow: var(--bs-btn-focus-box-shadow)
}

.btn-check:checked:focus-visible+.btn {
    box-shadow: var(--bs-btn-focus-box-shadow)
}

.btn.disabled,
.btn:disabled {
    background-color: var(--bs-btn-disabled-bg);
    border-color: var(--bs-btn-disabled-border-color);
    color: var(--bs-btn-disabled-color);
    opacity: var(--bs-btn-disabled-opacity);
    pointer-events: none
}

.btn-primary {
    --bs-btn-color: #fff;
    --bs-btn-bg: #dba87f;
    --bs-btn-border-color: #dba87f;
    --bs-btn-hover-color: #fff;
    --bs-btn-hover-bg: #e0b592;
    --bs-btn-hover-border-color: #dfb18c;
    --bs-btn-focus-shadow-rgb: 186, 143, 108;
    --bs-btn-active-color: #fff;
    --bs-btn-active-bg: #e2b999;
    --bs-btn-active-border-color: #dfb18c;
    --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, .125);
    --bs-btn-disabled-color: #fff;
    --bs-btn-disabled-bg: #dba87f;
    --bs-btn-disabled-border-color: #dba87f
}

.btn-secondary {
    --bs-btn-color: #000;
    --bs-btn-bg: #ebebeb;
    --bs-btn-border-color: #ebebeb;
    --bs-btn-hover-color: #000;
    --bs-btn-hover-bg: #eee;
    --bs-btn-hover-border-color: #ededed;
    --bs-btn-focus-shadow-rgb: 200, 200, 200;
    --bs-btn-active-color: #000;
    --bs-btn-active-bg: #efefef;
    --bs-btn-active-border-color: #ededed;
    --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, .125);
    --bs-btn-disabled-color: #000;
    --bs-btn-disabled-bg: #ebebeb;
    --bs-btn-disabled-border-color: #ebebeb
}

.btn-success {
    --bs-btn-color: #000;
    --bs-btn-bg: #22c05c;
    --bs-btn-border-color: #22c05c;
    --bs-btn-hover-color: #000;
    --bs-btn-hover-bg: #43c974;
    --bs-btn-hover-border-color: #38c66c;
    --bs-btn-focus-shadow-rgb: 29, 163, 78;
    --bs-btn-active-color: #000;
    --bs-btn-active-bg: #4ecd7d;
    --bs-btn-active-border-color: #38c66c;
    --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, .125);
    --bs-btn-disabled-color: #000;
    --bs-btn-disabled-bg: #22c05c;
    --bs-btn-disabled-border-color: #22c05c
}

.btn-info {
    --bs-btn-color: #000;
    --bs-btn-bg: #06b6d4;
    --bs-btn-border-color: #06b6d4;
    --bs-btn-hover-color: #000;
    --bs-btn-hover-bg: #2bc1da;
    --bs-btn-hover-border-color: #1fbdd8;
    --bs-btn-focus-shadow-rgb: 5, 155, 180;
    --bs-btn-active-color: #000;
    --bs-btn-active-bg: #38c5dd;
    --bs-btn-active-border-color: #1fbdd8;
    --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, .125);
    --bs-btn-disabled-color: #000;
    --bs-btn-disabled-bg: #06b6d4;
    --bs-btn-disabled-border-color: #06b6d4
}

.btn-warning {
    --bs-btn-color: #000;
    --bs-btn-bg: #eab308;
    --bs-btn-border-color: #eab308;
    --bs-btn-hover-color: #000;
    --bs-btn-hover-bg: #edbe2d;
    --bs-btn-hover-border-color: #ecbb21;
    --bs-btn-focus-shadow-rgb: 199, 152, 7;
    --bs-btn-active-color: #000;
    --bs-btn-active-bg: #eec239;
    --bs-btn-active-border-color: #ecbb21;
    --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, .125);
    --bs-btn-disabled-color: #000;
    --bs-btn-disabled-bg: #eab308;
    --bs-btn-disabled-border-color: #eab308
}

.btn-danger {
    --bs-btn-color: #000;
    --bs-btn-bg: #ef4444;
    --bs-btn-border-color: #ef4444;
    --bs-btn-hover-color: #000;
    --bs-btn-hover-bg: #f16060;
    --bs-btn-hover-border-color: #f15757;
    --bs-btn-focus-shadow-rgb: 203, 58, 58;
    --bs-btn-active-color: #000;
    --bs-btn-active-bg: #f26969;
    --bs-btn-active-border-color: #f15757;
    --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, .125);
    --bs-btn-disabled-color: #000;
    --bs-btn-disabled-bg: #ef4444;
    --bs-btn-disabled-border-color: #ef4444
}

.btn-light {
    --bs-btn-color: #000;
    --bs-btn-bg: #fafafa;
    --bs-btn-border-color: #fafafa;
    --bs-btn-hover-color: #000;
    --bs-btn-hover-bg: #d5d5d5;
    --bs-btn-hover-border-color: #c8c8c8;
    --bs-btn-focus-shadow-rgb: 213, 213, 213;
    --bs-btn-active-color: #000;
    --bs-btn-active-bg: #c8c8c8;
    --bs-btn-active-border-color: #bcbcbc;
    --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, .125);
    --bs-btn-disabled-color: #000;
    --bs-btn-disabled-bg: #fafafa;
    --bs-btn-disabled-border-color: #fafafa
}

.btn-dark {
    --bs-btn-color: #fff;
    --bs-btn-bg: #09090b;
    --bs-btn-border-color: #09090b;
    --bs-btn-hover-color: #fff;
    --bs-btn-hover-bg: #2e2e30;
    --bs-btn-hover-border-color: #222223;
    --bs-btn-focus-shadow-rgb: 46, 46, 48;
    --bs-btn-active-color: #fff;
    --bs-btn-active-bg: #3a3a3c;
    --bs-btn-active-border-color: #222223;
    --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, .125);
    --bs-btn-disabled-color: #fff;
    --bs-btn-disabled-bg: #09090b;
    --bs-btn-disabled-border-color: #09090b
}

.btn-outline-primary {
    --bs-btn-color: #dba87f;
    --bs-btn-border-color: #dba87f;
    --bs-btn-hover-color: #fff;
    --bs-btn-hover-bg: #dba87f;
    --bs-btn-hover-border-color: #dba87f;
    --bs-btn-focus-shadow-rgb: 219, 168, 127;
    --bs-btn-active-color: #fff;
    --bs-btn-active-bg: #dba87f;
    --bs-btn-active-border-color: #dba87f;
    --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, .125);
    --bs-btn-disabled-color: #dba87f;
    --bs-btn-disabled-bg: transparent;
    --bs-btn-disabled-border-color: #dba87f;
    --bs-gradient: none
}

.btn-outline-secondary {
    --bs-btn-color: #ebebeb;
    --bs-btn-border-color: #ebebeb;
    --bs-btn-hover-color: #000;
    --bs-btn-hover-bg: #ebebeb;
    --bs-btn-hover-border-color: #ebebeb;
    --bs-btn-focus-shadow-rgb: 235, 235, 235;
    --bs-btn-active-color: #000;
    --bs-btn-active-bg: #ebebeb;
    --bs-btn-active-border-color: #ebebeb;
    --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, .125);
    --bs-btn-disabled-color: #ebebeb;
    --bs-btn-disabled-bg: transparent;
    --bs-btn-disabled-border-color: #ebebeb;
    --bs-gradient: none
}

.btn-outline-success {
    --bs-btn-color: #22c05c;
    --bs-btn-border-color: #22c05c;
    --bs-btn-hover-color: #000;
    --bs-btn-hover-bg: #22c05c;
    --bs-btn-hover-border-color: #22c05c;
    --bs-btn-focus-shadow-rgb: 34, 192, 92;
    --bs-btn-active-color: #000;
    --bs-btn-active-bg: #22c05c;
    --bs-btn-active-border-color: #22c05c;
    --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, .125);
    --bs-btn-disabled-color: #22c05c;
    --bs-btn-disabled-bg: transparent;
    --bs-btn-disabled-border-color: #22c05c;
    --bs-gradient: none
}

.btn-outline-info {
    --bs-btn-color: #06b6d4;
    --bs-btn-border-color: #06b6d4;
    --bs-btn-hover-color: #000;
    --bs-btn-hover-bg: #06b6d4;
    --bs-btn-hover-border-color: #06b6d4;
    --bs-btn-focus-shadow-rgb: 6, 182, 212;
    --bs-btn-active-color: #000;
    --bs-btn-active-bg: #06b6d4;
    --bs-btn-active-border-color: #06b6d4;
    --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, .125);
    --bs-btn-disabled-color: #06b6d4;
    --bs-btn-disabled-bg: transparent;
    --bs-btn-disabled-border-color: #06b6d4;
    --bs-gradient: none
}

.btn-outline-warning {
    --bs-btn-color: #eab308;
    --bs-btn-border-color: #eab308;
    --bs-btn-hover-color: #000;
    --bs-btn-hover-bg: #eab308;
    --bs-btn-hover-border-color: #eab308;
    --bs-btn-focus-shadow-rgb: 234, 179, 8;
    --bs-btn-active-color: #000;
    --bs-btn-active-bg: #eab308;
    --bs-btn-active-border-color: #eab308;
    --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, .125);
    --bs-btn-disabled-color: #eab308;
    --bs-btn-disabled-bg: transparent;
    --bs-btn-disabled-border-color: #eab308;
    --bs-gradient: none
}

.btn-outline-danger {
    --bs-btn-color: #ef4444;
    --bs-btn-border-color: #ef4444;
    --bs-btn-hover-color: #000;
    --bs-btn-hover-bg: #ef4444;
    --bs-btn-hover-border-color: #ef4444;
    --bs-btn-focus-shadow-rgb: 239, 68, 68;
    --bs-btn-active-color: #000;
    --bs-btn-active-bg: #ef4444;
    --bs-btn-active-border-color: #ef4444;
    --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, .125);
    --bs-btn-disabled-color: #ef4444;
    --bs-btn-disabled-bg: transparent;
    --bs-btn-disabled-border-color: #ef4444;
    --bs-gradient: none
}

.btn-outline-light {
    --bs-btn-color: #fafafa;
    --bs-btn-border-color: #fafafa;
    --bs-btn-hover-color: #000;
    --bs-btn-hover-bg: #fafafa;
    --bs-btn-hover-border-color: #fafafa;
    --bs-btn-focus-shadow-rgb: 250, 250, 250;
    --bs-btn-active-color: #000;
    --bs-btn-active-bg: #fafafa;
    --bs-btn-active-border-color: #fafafa;
    --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, .125);
    --bs-btn-disabled-color: #fafafa;
    --bs-btn-disabled-bg: transparent;
    --bs-btn-disabled-border-color: #fafafa;
    --bs-gradient: none
}

.btn-outline-dark {
    --bs-btn-color: #09090b;
    --bs-btn-border-color: #09090b;
    --bs-btn-hover-color: #fff;
    --bs-btn-hover-bg: #09090b;
    --bs-btn-hover-border-color: #09090b;
    --bs-btn-focus-shadow-rgb: 9, 9, 11;
    --bs-btn-active-color: #fff;
    --bs-btn-active-bg: #09090b;
    --bs-btn-active-border-color: #09090b;
    --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, .125);
    --bs-btn-disabled-color: #09090b;
    --bs-btn-disabled-bg: transparent;
    --bs-btn-disabled-border-color: #09090b;
    --bs-gradient: none
}

.btn-link {
    --bs-btn-font-weight: 400;
    --bs-btn-color: var(--bs-link-color);
    --bs-btn-bg: transparent;
    --bs-btn-border-color: transparent;
    --bs-btn-hover-color: var(--bs-link-hover-color);
    --bs-btn-hover-border-color: transparent;
    --bs-btn-active-color: var(--bs-link-hover-color);
    --bs-btn-active-border-color: transparent;
    --bs-btn-disabled-color: #71717a;
    --bs-btn-disabled-border-color: transparent;
    --bs-btn-box-shadow: 0 0 0 #000;
    --bs-btn-focus-shadow-rgb: 71, 71, 74;
    text-decoration: underline
}

.btn-link:focus-visible {
    color: var(--bs-btn-color)
}

.btn-link:hover {
    color: var(--bs-btn-hover-color)
}

.btn-group-lg>.btn,
.btn-lg {
    --bs-btn-padding-y: 0.5rem;
    --bs-btn-padding-x: 1rem;
    --bs-btn-font-size: 1.1rem;
    --bs-btn-border-radius: var(--bs-border-radius-lg)
}

.btn-group-sm>.btn,
.btn-sm {
    --bs-btn-padding-y: 0.25rem;
    --bs-btn-padding-x: 0.5rem;
    --bs-btn-font-size: 0.76rem;
    --bs-btn-border-radius: var(--bs-border-radius-sm)
}

.fade {
    transition: opacity .15s linear
}

@media(prefers-reduced-motion:reduce) {
    .fade {
        transition: none
    }
}

.fade:not(.show) {
    opacity: 0
}

.collapse:not(.show) {
    display: none
}

.dropdown {
    position: relative
}

.dropdown-toggle {
    white-space: nowrap
}

.dropdown-toggle:after {
    border-bottom: 0;
    border-right: .3em solid transparent;
    border-left: .3em solid transparent;
    border-top: .3em solid;
    content: "";
    display: inline-block;
    margin-right: .255em;
    vertical-align: .255em
}

.dropdown-toggle:empty:after {
    margin-right: 0
}

.dropdown-menu {
    --bs-dropdown-zindex: 1000;
    --bs-dropdown-min-width: 10rem;
    --bs-dropdown-padding-x: 0;
    --bs-dropdown-padding-y: 0.5rem;
    --bs-dropdown-spacer: 0.125rem;
    --bs-dropdown-font-size: 0.95rem;
    --bs-dropdown-color: var(--bs-body-color);
    --bs-dropdown-bg: var(--bs-body-bg);
    --bs-dropdown-border-color: var(--bs-border-color-translucent);
    --bs-dropdown-border-radius: var(--bs-border-radius);
    --bs-dropdown-border-width: var(--bs-border-width);
    --bs-dropdown-inner-border-radius: calc(var(--bs-border-radius) - var(--bs-border-width));
    --bs-dropdown-divider-bg: var(--bs-border-color-translucent);
    --bs-dropdown-divider-margin-y: 0.5rem;
    --bs-dropdown-box-shadow: var(--bs-box-shadow);
    --bs-dropdown-link-color: var(--bs-body-color);
    --bs-dropdown-link-hover-color: var(--bs-body-color);
    --bs-dropdown-link-hover-bg: var(--bs-tertiary-bg);
    --bs-dropdown-link-active-color: #fff;
    --bs-dropdown-link-active-bg: #dba87f;
    --bs-dropdown-link-disabled-color: var(--bs-tertiary-color);
    --bs-dropdown-item-padding-x: 1rem;
    --bs-dropdown-item-padding-y: 0.25rem;
    --bs-dropdown-header-color: #71717a;
    --bs-dropdown-header-padding-x: 1rem;
    --bs-dropdown-header-padding-y: 0.5rem;
    background-clip: padding-box;
    background-color: var(--bs-dropdown-bg);
    border: var(--bs-dropdown-border-width) solid var(--bs-dropdown-border-color);
    border-radius: var(--bs-dropdown-border-radius);
    color: var(--bs-dropdown-color);
    display: none;
    font-size: var(--bs-dropdown-font-size);
    list-style: none;
    margin: 0;
    min-width: var(--bs-dropdown-min-width);
    padding: var(--bs-dropdown-padding-y) var(--bs-dropdown-padding-x);
    position: absolute;
    text-align: right;
    z-index: var(--bs-dropdown-zindex)
}

.dropdown-divider {
    border-top: 1px solid var(--bs-dropdown-divider-bg);
    height: 0;
    margin: var(--bs-dropdown-divider-margin-y) 0;
    opacity: 1;
    overflow: hidden
}

.dropdown-item {
    background-color: transparent;
    border: 0;
    border-radius: var(--bs-dropdown-item-border-radius, 0);
    clear: both;
    color: var(--bs-dropdown-link-color);
    display: block;
    font-weight: 400;
    padding: var(--bs-dropdown-item-padding-y) var(--bs-dropdown-item-padding-x);
    text-align: inherit;
    text-decoration: none;
    white-space: nowrap;
    width: 100%
}

.dropdown-item:focus,
.dropdown-item:hover {
    background-color: var(--bs-dropdown-link-hover-bg);
    color: var(--bs-dropdown-link-hover-color)
}

.dropdown-item.active,
.dropdown-item:active {
    background-color: var(--bs-dropdown-link-active-bg);
    color: var(--bs-dropdown-link-active-color);
    text-decoration: none
}

.dropdown-item.disabled,
.dropdown-item:disabled {
    background-color: transparent;
    color: var(--bs-dropdown-link-disabled-color);
    pointer-events: none
}

.dropdown-menu.show {
    display: block
}

.btn-group,
.btn-group-vertical {
    display: inline-flex;
    position: relative;
    vertical-align: middle
}

.btn-group-vertical>.btn,
.btn-group>.btn {
    flex: 1 1 auto;
    position: relative
}

.btn-group-vertical>.btn-check:checked+.btn,
.btn-group-vertical>.btn-check:focus+.btn,
.btn-group-vertical>.btn.active,
.btn-group-vertical>.btn:active,
.btn-group-vertical>.btn:focus,
.btn-group-vertical>.btn:hover,
.btn-group>.btn-check:checked+.btn,
.btn-group>.btn-check:focus+.btn,
.btn-group>.btn.active,
.btn-group>.btn:active,
.btn-group>.btn:focus,
.btn-group>.btn:hover {
    z-index: 1
}

.btn-toolbar {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start
}

.btn-toolbar .input-group {
    width: auto
}

.btn-group {
    border-radius: var(--bs-border-radius)
}

.btn-group>.btn-group:not(:first-child),
.btn-group>:not(.btn-check:first-child)+.btn {
    margin-right: calc(var(--bs-border-width)*-1)
}

.btn-group>.btn-group:not(:last-child)>.btn,
.btn-group>.btn:not(:last-child):not(.dropdown-toggle) {
    border-bottom-left-radius: 0;
    border-top-left-radius: 0
}

.btn-group>.btn-group:not(:first-child)>.btn,
.btn-group>.btn:nth-child(n+3),
.btn-group>:not(.btn-check)+.btn {
    border-bottom-right-radius: 0;
    border-top-right-radius: 0
}

.btn-group-vertical {
    align-items: flex-start;
    flex-direction: column;
    justify-content: center
}

.btn-group-vertical>.btn,
.btn-group-vertical>.btn-group {
    width: 100%
}

.btn-group-vertical>.btn-group:not(:first-child),
.btn-group-vertical>.btn:not(:first-child) {
    margin-top: calc(var(--bs-border-width)*-1)
}

.btn-group-vertical>.btn-group:not(:last-child)>.btn,
.btn-group-vertical>.btn:not(:last-child):not(.dropdown-toggle) {
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0
}

.btn-group-vertical>.btn-group:not(:first-child)>.btn,
.btn-group-vertical>.btn~.btn {
    border-top-right-radius: 0;
    border-top-left-radius: 0
}

.nav {
    --bs-nav-link-padding-x: 1rem;
    --bs-nav-link-padding-y: 0.5rem;
    --bs-nav-link-font-weight: ;
    --bs-nav-link-color: var(--bs-link-color);
    --bs-nav-link-hover-color: var(--bs-link-hover-color);
    --bs-nav-link-disabled-color: var(--bs-secondary-color);
    display: flex;
    flex-wrap: wrap;
    list-style: none;
    margin-bottom: 0;
    padding-right: 0
}

.nav-link {
    background: none;
    border: 0;
    color: var(--bs-nav-link-color);
    display: block;
    font-size: var(--bs-nav-link-font-size);
    font-weight: var(--bs-nav-link-font-weight);
    padding: var(--bs-nav-link-padding-y) var(--bs-nav-link-padding-x);
    text-decoration: none;
    transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out
}

@media(prefers-reduced-motion:reduce) {
    .nav-link {
        transition: none
    }
}

.nav-link:focus,
.nav-link:hover {
    color: var(--bs-nav-link-hover-color)
}

.nav-link:focus-visible {
    box-shadow: 0 0 0 .25rem hsla(27, 56%, 68%, .25);
    outline: 0
}

.nav-link.disabled,
.nav-link:disabled {
    color: var(--bs-nav-link-disabled-color);
    cursor: default;
    pointer-events: none
}

.card {
    --bs-card-spacer-y: 1rem;
    --bs-card-spacer-x: 1rem;
    --bs-card-title-spacer-y: 0.5rem;
    --bs-card-title-color: ;
    --bs-card-subtitle-color: ;
    --bs-card-border-width: var(--bs-border-width);
    --bs-card-border-color: #f4f4f5;
    --bs-card-border-radius: var(--bs-border-radius);
    --bs-card-box-shadow: ;
    --bs-card-inner-border-radius: calc(var(--bs-border-radius) - var(--bs-border-width));
    --bs-card-cap-padding-y: 0.5rem;
    --bs-card-cap-padding-x: 1rem;
    --bs-card-cap-bg: rgba(var(--bs-body-color-rgb), 0.03);
    --bs-card-cap-color: ;
    --bs-card-height: ;
    --bs-card-color: ;
    --bs-card-bg: var(--bs-body-bg);
    --bs-card-img-overlay-padding: 1rem;
    --bs-card-group-margin: 0.75rem;
    color: var(--bs-body-color);
    display: flex;
    flex-direction: column;
    height: var(--bs-card-height);
    min-width: 0;
    position: relative;
    word-wrap: break-word;
    background-clip: border-box;
    background-color: var(--bs-card-bg);
    border: var(--bs-card-border-width) solid var(--bs-card-border-color);
    border-radius: var(--bs-card-border-radius)
}

.card-body {
    color: var(--bs-card-color);
    flex: 1 1 auto;
    padding: var(--bs-card-spacer-y) var(--bs-card-spacer-x)
}

.card-title {
    color: var(--bs-card-title-color);
    margin-bottom: var(--bs-card-title-spacer-y)
}

.card-text:last-child {
    margin-bottom: 0
}

.card-header {
    background-color: var(--bs-card-cap-bg);
    border-bottom: var(--bs-card-border-width) solid var(--bs-card-border-color);
    color: var(--bs-card-cap-color);
    margin-bottom: 0;
    padding: var(--bs-card-cap-padding-y) var(--bs-card-cap-padding-x)
}

.card-header:first-child {
    border-radius: var(--bs-card-inner-border-radius) var(--bs-card-inner-border-radius) 0 0
}

.card-footer {
    background-color: var(--bs-card-cap-bg);
    border-top: var(--bs-card-border-width) solid var(--bs-card-border-color);
    color: var(--bs-card-cap-color);
    padding: var(--bs-card-cap-padding-y) var(--bs-card-cap-padding-x)
}

.card-footer:last-child {
    border-radius: 0 0 var(--bs-card-inner-border-radius) var(--bs-card-inner-border-radius)
}

.card-img-top {
    width: 100%
}

.card-img-top {
    border-top-right-radius: var(--bs-card-inner-border-radius);
    border-top-left-radius: var(--bs-card-inner-border-radius)
}

.accordion {
    --bs-accordion-color: var(--bs-body-color);
    --bs-accordion-bg: var(--bs-body-bg);
    --bs-accordion-transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out, border-radius 0.15s ease;
    --bs-accordion-border-color: var(--bs-border-color);
    --bs-accordion-border-width: var(--bs-border-width);
    --bs-accordion-border-radius: var(--bs-border-radius);
    --bs-accordion-inner-border-radius: calc(var(--bs-border-radius) - var(--bs-border-width));
    --bs-accordion-btn-padding-x: 1.25rem;
    --bs-accordion-btn-padding-y: 1rem;
    --bs-accordion-btn-color: var(--bs-body-color);
    --bs-accordion-btn-bg: var(--bs-accordion-bg);
    --bs-accordion-btn-icon: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' stroke='%2327272a' stroke-linecap='round' stroke-linejoin='round' viewBox='0 0 16 16'%3E%3Cpath d='m2 5 6 6 6-6'/%3E%3C/svg%3E");
    --bs-accordion-btn-icon-width: 1.25rem;
    --bs-accordion-btn-icon-transform: rotate(-180deg);
    --bs-accordion-btn-icon-transition: transform 0.2s ease-in-out;
    --bs-accordion-btn-active-icon: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' stroke='%23584333' stroke-linecap='round' stroke-linejoin='round' viewBox='0 0 16 16'%3E%3Cpath d='m2 5 6 6 6-6'/%3E%3C/svg%3E");
    --bs-accordion-btn-focus-box-shadow: none;
    --bs-accordion-body-padding-x: 1.25rem;
    --bs-accordion-body-padding-y: 1rem;
    --bs-accordion-active-color: var(--bs-primary-text-emphasis);
    --bs-accordion-active-bg: var(--bs-primary-bg-subtle)
}

.accordion-item {
    background-color: var(--bs-accordion-bg);
    border: var(--bs-accordion-border-width) solid var(--bs-accordion-border-color);
    color: var(--bs-accordion-color)
}

.accordion-item:first-of-type {
    border-top-right-radius: var(--bs-accordion-border-radius);
    border-top-left-radius: var(--bs-accordion-border-radius)
}

.accordion-item:not(:first-of-type) {
    border-top: 0
}

.accordion-item:last-of-type {
    border-bottom-right-radius: var(--bs-accordion-border-radius);
    border-bottom-left-radius: var(--bs-accordion-border-radius)
}

.accordion-item:last-of-type>.accordion-collapse {
    border-bottom-right-radius: var(--bs-accordion-border-radius);
    border-bottom-left-radius: var(--bs-accordion-border-radius)
}

.accordion-flush>.accordion-item {
    border-right: 0;
    border-radius: 0;
    border-left: 0
}

.accordion-flush>.accordion-item:first-child {
    border-top: 0
}

.accordion-flush>.accordion-item:last-child {
    border-bottom: 0
}

.accordion-flush>.accordion-item>.accordion-collapse {
    border-radius: 0
}

.breadcrumb {
    --bs-breadcrumb-padding-x: 0;
    --bs-breadcrumb-padding-y: 0;
    --bs-breadcrumb-margin-bottom: 1rem;
    --bs-breadcrumb-font-size: 0.875rem;
    --bs-breadcrumb-bg: none;
    --bs-breadcrumb-border-radius: ;
    --bs-breadcrumb-divider-color: var(--bs-secondary-color);
    --bs-breadcrumb-item-padding-x: 0.5rem;
    --bs-breadcrumb-item-active-color: var(--bs-secondary-color);
    background-color: var(--bs-breadcrumb-bg);
    border-radius: var(--bs-breadcrumb-border-radius);
    display: flex;
    flex-wrap: wrap;
    font-size: var(--bs-breadcrumb-font-size);
    list-style: none;
    margin-bottom: var(--bs-breadcrumb-margin-bottom);
    padding: var(--bs-breadcrumb-padding-y) var(--bs-breadcrumb-padding-x)
}

.breadcrumb-item+.breadcrumb-item {
    padding-right: var(--bs-breadcrumb-item-padding-x)
}

.breadcrumb-item+.breadcrumb-item:before {
    color: var(--bs-breadcrumb-divider-color);
    content: var(--bs-breadcrumb-divider, "/");
    float: right;
    padding-left: var(--bs-breadcrumb-item-padding-x)
}

.breadcrumb-item.active {
    color: var(--bs-breadcrumb-item-active-color)
}

.pagination {
    --bs-pagination-padding-x: 0.75rem;
    --bs-pagination-padding-y: 0.375rem;
    --bs-pagination-font-size: 0.95rem;
    --bs-pagination-color: var(--bs-link-color);
    --bs-pagination-bg: var(--bs-body-bg);
    --bs-pagination-border-width: var(--bs-border-width);
    --bs-pagination-border-color: var(--bs-border-color);
    --bs-pagination-border-radius: var(--bs-border-radius);
    --bs-pagination-hover-color: var(--bs-link-hover-color);
    --bs-pagination-hover-bg: var(--bs-tertiary-bg);
    --bs-pagination-hover-border-color: var(--bs-border-color);
    --bs-pagination-focus-color: var(--bs-link-hover-color);
    --bs-pagination-focus-bg: var(--bs-secondary-bg);
    --bs-pagination-focus-box-shadow: 0 0 0 0.25rem hsla(27, 56%, 68%, .25);
    --bs-pagination-active-color: #fff;
    --bs-pagination-active-bg: #dba87f;
    --bs-pagination-active-border-color: #dba87f;
    --bs-pagination-disabled-color: var(--bs-secondary-color);
    --bs-pagination-disabled-bg: var(--bs-secondary-bg);
    --bs-pagination-disabled-border-color: var(--bs-border-color);
    display: flex;
    list-style: none;
    padding-right: 0
}

.badge {
    --bs-badge-padding-x: 0.6em;
    --bs-badge-padding-y: 0.5em;
    --bs-badge-font-size: 65%;
    --bs-badge-font-weight: 700;
    --bs-badge-color: #fff;
    --bs-badge-border-radius: 0.025rem;
    border-radius: var(--bs-badge-border-radius);
    color: var(--bs-badge-color);
    display: inline-block;
    font-size: var(--bs-badge-font-size);
    font-weight: var(--bs-badge-font-weight);
    line-height: 1;
    padding: var(--bs-badge-padding-y) var(--bs-badge-padding-x);
    text-align: center;
    vertical-align: baseline;
    white-space: nowrap
}

.badge:empty {
    display: none
}

.btn .badge {
    position: relative;
    top: -1px
}

.alert {
    --bs-alert-bg: transparent;
    --bs-alert-padding-x: 1rem;
    --bs-alert-padding-y: 1rem;
    --bs-alert-margin-bottom: 1rem;
    --bs-alert-color: inherit;
    --bs-alert-border-color: transparent;
    --bs-alert-border: var(--bs-border-width) solid var(--bs-alert-border-color);
    --bs-alert-border-radius: var(--bs-border-radius);
    --bs-alert-link-color: inherit;
    background-color: var(--bs-alert-bg);
    border: var(--bs-alert-border);
    border-radius: var(--bs-alert-border-radius);
    color: var(--bs-alert-color);
    margin-bottom: var(--bs-alert-margin-bottom);
    padding: var(--bs-alert-padding-y) var(--bs-alert-padding-x);
    position: relative
}

.alert-heading {
    color: inherit
}

.alert-success {
    --bs-alert-color: var(--bs-success-text-emphasis);
    --bs-alert-bg: var(--bs-success-bg-subtle);
    --bs-alert-border-color: var(--bs-success-border-subtle);
    --bs-alert-link-color: var(--bs-success-text-emphasis)
}

.alert-info {
    --bs-alert-color: var(--bs-info-text-emphasis);
    --bs-alert-bg: var(--bs-info-bg-subtle);
    --bs-alert-border-color: var(--bs-info-border-subtle);
    --bs-alert-link-color: var(--bs-info-text-emphasis)
}

.alert-warning {
    --bs-alert-color: var(--bs-warning-text-emphasis);
    --bs-alert-bg: var(--bs-warning-bg-subtle);
    --bs-alert-border-color: var(--bs-warning-border-subtle);
    --bs-alert-link-color: var(--bs-warning-text-emphasis)
}

.alert-danger {
    --bs-alert-color: var(--bs-danger-text-emphasis);
    --bs-alert-bg: var(--bs-danger-bg-subtle);
    --bs-alert-border-color: var(--bs-danger-border-subtle);
    --bs-alert-link-color: var(--bs-danger-text-emphasis)
}

@keyframes progress-bar-stripes {
    0% {
        background-position-x: 1rem
    }
}

.progress {
    --bs-progress-height: 1rem;
    --bs-progress-font-size: 0.7125rem;
    --bs-progress-bg: var(--bs-secondary-bg);
    --bs-progress-border-radius: var(--bs-border-radius);
    --bs-progress-box-shadow: var(--bs-box-shadow-inset);
    --bs-progress-bar-color: #fff;
    --bs-progress-bar-bg: #dba87f;
    --bs-progress-bar-transition: width 0.6s ease;
    background-color: var(--bs-progress-bg);
    border-radius: var(--bs-progress-border-radius);
    display: flex;
    font-size: var(--bs-progress-font-size);
    height: var(--bs-progress-height);
    overflow: hidden
}

.progress-bar {
    background-color: var(--bs-progress-bar-bg);
    color: var(--bs-progress-bar-color);
    display: flex;
    flex-direction: column;
    justify-content: center;
    overflow: hidden;
    text-align: center;
    transition: var(--bs-progress-bar-transition);
    white-space: nowrap
}

@media(prefers-reduced-motion:reduce) {
    .progress-bar {
        transition: none
    }
}

.list-group-item {
    background-color: var(--bs-list-group-bg);
    border: var(--bs-list-group-border-width) solid var(--bs-list-group-border-color);
    color: var(--bs-list-group-color);
    display: block;
    padding: var(--bs-list-group-item-padding-y) var(--bs-list-group-item-padding-x);
    position: relative;
    text-decoration: none
}

.list-group-item:first-child {
    border-top-right-radius: inherit;
    border-top-left-radius: inherit
}

.list-group-item:last-child {
    border-bottom-right-radius: inherit;
    border-bottom-left-radius: inherit
}

.list-group-item.disabled,
.list-group-item:disabled {
    background-color: var(--bs-list-group-disabled-bg);
    color: var(--bs-list-group-disabled-color);
    pointer-events: none
}

.list-group-item.active {
    background-color: var(--bs-list-group-active-bg);
    border-color: var(--bs-list-group-active-border-color);
    color: var(--bs-list-group-active-color);
    z-index: 2
}

.list-group-item+.list-group-item {
    border-top-width: 0
}

.list-group-item+.list-group-item.active {
    border-top-width: var(--bs-list-group-border-width);
    margin-top: calc(var(--bs-list-group-border-width)*-1)
}

.btn-close {
    --bs-btn-close-color: #000;
    --bs-btn-close-bg: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3E%3Cpath d='M.293.293a1 1 0 0 1 1.414 0L8 6.586 14.293.293a1 1 0 1 1 1.414 1.414L9.414 8l6.293 6.293a1 1 0 0 1-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 0 1-1.414-1.414L6.586 8 .293 1.707a1 1 0 0 1 0-1.414'/%3E%3C/svg%3E");
    --bs-btn-close-opacity: 0.5;
    --bs-btn-close-hover-opacity: 0.75;
    --bs-btn-close-focus-shadow: 0 0 0 0.25rem hsla(27, 56%, 68%, .25);
    --bs-btn-close-focus-opacity: 1;
    --bs-btn-close-disabled-opacity: 0.25;
    --bs-btn-close-white-filter: invert(1) grayscale(100%) brightness(200%);
    background: transparent var(--bs-btn-close-bg) center/1em auto no-repeat;
    border: 0;
    border-radius: .5rem;
    box-sizing: content-box;
    height: 1em;
    opacity: var(--bs-btn-close-opacity);
    padding: .25em;
    width: 1em
}

.btn-close,
.btn-close:hover {
    color: var(--bs-btn-close-color)
}

.btn-close:hover {
    opacity: var(--bs-btn-close-hover-opacity);
    text-decoration: none
}

.btn-close:focus {
    box-shadow: var(--bs-btn-close-focus-shadow);
    opacity: var(--bs-btn-close-focus-opacity);
    outline: 0
}

.btn-close.disabled,
.btn-close:disabled {
    opacity: var(--bs-btn-close-disabled-opacity);
    pointer-events: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.btn-close-white {
    filter: var(--bs-btn-close-white-filter)
}

.toast {
    --bs-toast-zindex: 1090;
    --bs-toast-padding-x: 0.75rem;
    --bs-toast-padding-y: 0.5rem;
    --bs-toast-spacing: 1.5rem;
    --bs-toast-max-width: 350px;
    --bs-toast-font-size: 0.875rem;
    --bs-toast-color: ;
    --bs-toast-bg: rgba(var(--bs-body-bg-rgb), 0.85);
    --bs-toast-border-width: var(--bs-border-width);
    --bs-toast-border-color: var(--bs-border-color-translucent);
    --bs-toast-border-radius: var(--bs-border-radius);
    --bs-toast-box-shadow: var(--bs-box-shadow);
    --bs-toast-header-color: var(--bs-secondary-color);
    --bs-toast-header-bg: rgba(var(--bs-body-bg-rgb), 0.85);
    --bs-toast-header-border-color: var(--bs-border-color-translucent);
    background-clip: padding-box;
    background-color: var(--bs-toast-bg);
    border: var(--bs-toast-border-width) solid var(--bs-toast-border-color);
    border-radius: var(--bs-toast-border-radius);
    box-shadow: var(--bs-toast-box-shadow);
    color: var(--bs-toast-color);
    font-size: var(--bs-toast-font-size);
    max-width: 100%;
    pointer-events: auto;
    width: var(--bs-toast-max-width)
}

.toast:not(.show) {
    display: none
}

.toast-container {
    --bs-toast-zindex: 1090;
    max-width: 100%;
    pointer-events: none;
    position: absolute;
    width: -moz-max-content;
    width: max-content;
    z-index: var(--bs-toast-zindex)
}

.toast-container>:not(:last-child) {
    margin-bottom: var(--bs-toast-spacing)
}

.toast-header {
    align-items: center;
    background-clip: padding-box;
    background-color: var(--bs-toast-header-bg);
    border-bottom: var(--bs-toast-border-width) solid var(--bs-toast-header-border-color);
    border-top-right-radius: calc(var(--bs-toast-border-radius) - var(--bs-toast-border-width));
    border-top-left-radius: calc(var(--bs-toast-border-radius) - var(--bs-toast-border-width));
    color: var(--bs-toast-header-color);
    display: flex;
    padding: var(--bs-toast-padding-y) var(--bs-toast-padding-x)
}

.toast-header .btn-close {
    margin-right: var(--bs-toast-padding-x);
    margin-left: calc(var(--bs-toast-padding-x)*-.5)
}

.toast-body {
    padding: var(--bs-toast-padding-x);
    word-wrap: break-word
}

.modal {
    --bs-modal-zindex: 1055;
    --bs-modal-width: 500px;
    --bs-modal-padding: 1rem;
    --bs-modal-margin: 0.5rem;
    --bs-modal-color: ;
    --bs-modal-bg: var(--bs-body-bg);
    --bs-modal-border-color: #e4e4e7;
    --bs-modal-border-width: var(--bs-border-width);
    --bs-modal-border-radius: var(--bs-border-radius-lg);
    --bs-modal-box-shadow: var(--bs-box-shadow-sm);
    --bs-modal-inner-border-radius: calc(var(--bs-border-radius-lg) - var(--bs-border-width));
    --bs-modal-header-padding-x: 1rem;
    --bs-modal-header-padding-y: 1rem;
    --bs-modal-header-padding: 1rem 1rem;
    --bs-modal-header-border-color: #f4f4f5;
    --bs-modal-header-border-width: var(--bs-border-width);
    --bs-modal-title-line-height: 1.5;
    --bs-modal-footer-gap: 0.5rem;
    --bs-modal-footer-bg: ;
    --bs-modal-footer-border-color: #f4f4f5;
    --bs-modal-footer-border-width: var(--bs-border-width);
    display: none;
    height: 100%;
    right: 0;
    outline: 0;
    overflow-x: hidden;
    overflow-y: auto;
    position: fixed;
    top: 0;
    width: 100%;
    z-index: var(--bs-modal-zindex)
}

.modal-dialog {
    margin: var(--bs-modal-margin);
    pointer-events: none;
    position: relative;
    width: auto
}

.modal.fade .modal-dialog {
    transform: translateY(-50px);
    transition: transform .3s ease-out
}

@media(prefers-reduced-motion:reduce) {
    .modal.fade .modal-dialog {
        transition: none
    }
}

.modal.show .modal-dialog {
    transform: none
}

.modal-dialog-centered {
    align-items: center;
    display: flex;
    min-height: calc(100% - var(--bs-modal-margin)*2)
}

.modal-content {
    background-clip: padding-box;
    background-color: var(--bs-modal-bg);
    border: var(--bs-modal-border-width) solid var(--bs-modal-border-color);
    border-radius: var(--bs-modal-border-radius);
    color: var(--bs-modal-color);
    display: flex;
    flex-direction: column;
    outline: 0;
    pointer-events: auto;
    position: relative;
    width: 100%
}

.modal-backdrop {
    --bs-backdrop-zindex: 1050;
    --bs-backdrop-bg: #000;
    --bs-backdrop-opacity: 0.6;
    background-color: var(--bs-backdrop-bg);
    height: 100vh;
    right: 0;
    position: fixed;
    top: 0;
    width: 100vw;
    z-index: var(--bs-backdrop-zindex)
}

.modal-backdrop.fade {
    opacity: 0
}

.modal-backdrop.show {
    opacity: var(--bs-backdrop-opacity)
}

.modal-body {
    flex: 1 1 auto;
    padding: var(--bs-modal-padding);
    position: relative
}

@media(min-width:576px) {
    .modal {
        --bs-modal-margin: 1.75rem;
        --bs-modal-box-shadow: var(--bs-box-shadow)
    }
    .modal-dialog {
        margin-right: auto;
        margin-left: auto;
        max-width: var(--bs-modal-width)
    }
}

@media(min-width:992px) {
    .modal-lg {
        --bs-modal-width: 800px
    }
}

.tooltip {
    --bs-tooltip-zindex: 1080;
    --bs-tooltip-max-width: 200px;
    --bs-tooltip-padding-x: 0.5rem;
    --bs-tooltip-padding-y: 0.25rem;
    --bs-tooltip-margin: ;
    --bs-tooltip-font-size: 0.76rem;
    --bs-tooltip-color: var(--bs-body-bg);
    --bs-tooltip-bg: var(--bs-emphasis-color);
    --bs-tooltip-border-radius: var(--bs-border-radius);
    --bs-tooltip-opacity: 0.9;
    --bs-tooltip-arrow-width: 0.8rem;
    --bs-tooltip-arrow-height: 0.4rem;
    display: block;
    font-family: var(--bs-font-sans-serif);
    font-size: var(--bs-tooltip-font-size);
    font-style: normal;
    font-weight: 400;
    letter-spacing: normal;
    line-break: auto;
    line-height: 1.5;
    margin: var(--bs-tooltip-margin);
    text-align: right;
    text-align: start;
    text-decoration: none;
    text-shadow: none;
    text-transform: none;
    white-space: normal;
    word-break: normal;
    word-spacing: normal;
    z-index: var(--bs-tooltip-zindex);
    word-wrap: break-word;
    opacity: 0
}

.tooltip.show {
    opacity: var(--bs-tooltip-opacity)
}

.carousel {
    position: relative
}

.carousel-inner {
    overflow: hidden;
    position: relative;
    width: 100%
}

.carousel-inner:after {
    clear: both;
    content: "";
    display: block
}

.carousel-item {
    backface-visibility: hidden;
    display: none;
    float: right;
    margin-left: -100%;
    position: relative;
    transition: transform .6s ease-in-out;
    width: 100%
}

@media(prefers-reduced-motion:reduce) {
    .carousel-item {
        transition: none
    }
}

.carousel-item.active {
    display: block
}

.carousel-control-next,
.carousel-control-prev {
    align-items: center;
    background: none;
    border: 0;
    bottom: 0;
    color: #fff;
    display: flex;
    justify-content: center;
    opacity: .5;
    padding: 0;
    position: absolute;
    text-align: center;
    top: 0;
    transition: opacity .15s ease;
    width: 15%;
    z-index: 1
}

@media(prefers-reduced-motion:reduce) {
    .carousel-control-next,
    .carousel-control-prev {
        transition: none
    }
}

.carousel-control-next:focus,
.carousel-control-next:hover,
.carousel-control-prev:focus,
.carousel-control-prev:hover {
    color: #fff;
    opacity: .9;
    outline: 0;
    text-decoration: none
}

.carousel-control-prev {
    right: 0
}

.carousel-control-next {
    left: 0
}

.carousel-control-next-icon,
.carousel-control-prev-icon {
    background-position: 50%;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    display: inline-block;
    height: 2rem;
    width: 2rem
}

.carousel-control-prev-icon {
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23fff' viewBox='0 0 16 16'%3E%3Cpath d='M11.354 1.646a.5.5 0 0 1 0 .708L5.707 8l5.647 5.646a.5.5 0 0 1-.708.708l-6-6a.5.5 0 0 1 0-.708l6-6a.5.5 0 0 1 .708 0'/%3E%3C/svg%3E")
}

.carousel-control-next-icon {
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23fff' viewBox='0 0 16 16'%3E%3Cpath d='M4.646 1.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1 0 .708l-6 6a.5.5 0 0 1-.708-.708L10.293 8 4.646 2.354a.5.5 0 0 1 0-.708'/%3E%3C/svg%3E")
}

.carousel-indicators {
    bottom: 0;
    display: flex;
    justify-content: center;
    right: 0;
    margin-bottom: 1rem;
    margin-right: 15%;
    margin-left: 15%;
    padding: 0;
    position: absolute;
    left: 0;
    z-index: 2
}

.carousel-indicators [data-bs-target] {
    background-clip: padding-box;
    background-color: #fff;
    border: 0;
    border-bottom: 10px solid transparent;
    border-top: 10px solid transparent;
    box-sizing: content-box;
    cursor: pointer;
    flex: 0 1 auto;
    height: 3px;
    margin-right: 3px;
    margin-left: 3px;
    opacity: .5;
    padding: 0;
    text-indent: -999px;
    transition: opacity .6s ease;
    width: 30px
}

@media(prefers-reduced-motion:reduce) {
    .carousel-indicators [data-bs-target] {
        transition: none
    }
}

.carousel-indicators .active {
    opacity: 1
}

.carousel-caption {
    bottom: 1.25rem;
    color: #fff;
    right: 15%;
    padding-bottom: 1.25rem;
    padding-top: 1.25rem;
    position: absolute;
    left: 15%;
    text-align: center
}

.spinner-border {
    animation: var(--bs-spinner-animation-speed) linear infinite var(--bs-spinner-animation-name);
    border-radius: 50%;
    display: inline-block;
    height: var(--bs-spinner-height);
    vertical-align: var(--bs-spinner-vertical-align);
    width: var(--bs-spinner-width)
}

@keyframes spinner-border {
    to {
        transform: rotate(-1turn)
    }
}

.spinner-border {
    --bs-spinner-width: 2rem;
    --bs-spinner-height: 2rem;
    --bs-spinner-vertical-align: -0.125em;
    --bs-spinner-border-width: 0.25em;
    --bs-spinner-animation-speed: 0.75s;
    --bs-spinner-animation-name: spinner-border;
    border-left-color: currentcolor;
    border: var(--bs-spinner-border-width) solid;
    border-left: var(--bs-spinner-border-width) solid transparent
}

.spinner-border-sm {
    --bs-spinner-width: 1rem;
    --bs-spinner-height: 1rem;
    --bs-spinner-border-width: 0.2em
}

@keyframes spinner-grow {
    0% {
        transform: scale(0)
    }
    50% {
        opacity: 1;
        transform: none
    }
}

@media(prefers-reduced-motion:reduce) {
    .spinner-border {
        --bs-spinner-animation-speed: 1.5s
    }
}

.offcanvas {
    --bs-offcanvas-zindex: 1045;
    --bs-offcanvas-width: 400px;
    --bs-offcanvas-height: 30vh;
    --bs-offcanvas-padding-x: 1rem;
    --bs-offcanvas-padding-y: 1rem;
    --bs-offcanvas-color: var(--bs-body-color);
    --bs-offcanvas-bg: var(--bs-body-bg);
    --bs-offcanvas-border-width: var(--bs-border-width);
    --bs-offcanvas-border-color: #e4e4e7;
    --bs-offcanvas-box-shadow: var(--bs-box-shadow-sm);
    --bs-offcanvas-transition: transform 0.3s ease-in-out;
    --bs-offcanvas-title-line-height: 1.5
}

.offcanvas {
    background-clip: padding-box;
    background-color: var(--bs-offcanvas-bg);
    bottom: 0;
    color: var(--bs-offcanvas-color);
    display: flex;
    flex-direction: column;
    max-width: 100%;
    outline: 0;
    position: fixed;
    transition: var(--bs-offcanvas-transition);
    visibility: hidden;
    z-index: var(--bs-offcanvas-zindex)
}

@media(prefers-reduced-motion:reduce) {
    .offcanvas {
        transition: none
    }
}

.offcanvas.offcanvas-start {
    border-left: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    right: 0;
    top: 0;
    transform: translateX(100%);
    width: var(--bs-offcanvas-width)
}

.offcanvas.offcanvas-end {
    border-right: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    left: 0;
    top: 0;
    transform: translateX(-100%);
    width: var(--bs-offcanvas-width)
}

.offcanvas.show:not(.hiding) {
    transform: none
}

.offcanvas.show {
    visibility: visible
}

.offcanvas-backdrop {
    background-color: #000;
    height: 100vh;
    right: 0;
    position: fixed;
    top: 0;
    width: 100vw;
    z-index: 1040
}

.offcanvas-backdrop.fade {
    opacity: 0
}

.offcanvas-backdrop.show {
    opacity: .6
}

.offcanvas-header {
    align-items: center;
    display: flex;
    padding: var(--bs-offcanvas-padding-y) var(--bs-offcanvas-padding-x)
}

.offcanvas-header .btn-close {
    margin: calc(var(--bs-offcanvas-padding-y)*-.5) auto calc(var(--bs-offcanvas-padding-y)*-.5) calc(var(--bs-offcanvas-padding-x)*-.5);
    padding: calc(var(--bs-offcanvas-padding-y)*.5) calc(var(--bs-offcanvas-padding-x)*.5)
}

.offcanvas-title {
    line-height: var(--bs-offcanvas-title-line-height);
    margin-bottom: 0
}

.offcanvas-body {
    flex-grow: 1;
    overflow-y: auto;
    padding: var(--bs-offcanvas-padding-y) var(--bs-offcanvas-padding-x)
}

.placeholder {
    background-color: currentcolor;
    cursor: wait;
    display: inline-block;
    min-height: 1em;
    opacity: .5;
    vertical-align: middle
}

.placeholder.btn:before {
    content: "";
    display: inline-block
}

@keyframes placeholder-glow {
    50% {
        opacity: .2
    }
}

@keyframes placeholder-wave {
    to {
        -webkit-mask-position: -200% 0;
        mask-position: -200% 0
    }
}

.text-bg-primary {
    background-color: RGBA(var(--bs-primary-rgb), var(--bs-bg-opacity, 1)) !important;
    color: #fff !important
}

.text-bg-secondary {
    background-color: RGBA(var(--bs-secondary-rgb), var(--bs-bg-opacity, 1)) !important;
    color: #000 !important
}

.text-bg-success {
    background-color: RGBA(var(--bs-success-rgb), var(--bs-bg-opacity, 1)) !important;
    color: #000 !important
}

.text-bg-info {
    background-color: RGBA(var(--bs-info-rgb), var(--bs-bg-opacity, 1)) !important;
    color: #000 !important
}

.text-bg-warning {
    background-color: RGBA(var(--bs-warning-rgb), var(--bs-bg-opacity, 1)) !important;
    color: #000 !important
}

.text-bg-danger {
    background-color: RGBA(var(--bs-danger-rgb), var(--bs-bg-opacity, 1)) !important;
    color: #000 !important
}

.text-bg-light {
    background-color: RGBA(var(--bs-light-rgb), var(--bs-bg-opacity, 1)) !important;
    color: #000 !important
}

.text-bg-dark {
    background-color: RGBA(var(--bs-dark-rgb), var(--bs-bg-opacity, 1)) !important;
    color: #fff !important
}

.ratio-1x1 {
    --bs-aspect-ratio: 100%
}

.hstack {
    align-items: center;
    flex-direction: row
}

.hstack,
.vstack {
    align-self: stretch;
    display: flex
}

.vstack {
    flex: 1 1 auto;
    flex-direction: column
}

.visually-hidden {
    height: 1px !important;
    margin: -1px !important;
    overflow: hidden !important;
    padding: 0 !important;
    width: 1px !important;
    clip: rect(0, 0, 0, 0) !important;
    border: 0 !important;
    white-space: nowrap !important
}

.visually-hidden:not(caption) {
    position: absolute !important
}

.object-fit-contain {
    -o-object-fit: contain !important;
    object-fit: contain !important
}

.object-fit-cover {
    -o-object-fit: cover !important;
    object-fit: cover !important
}

.opacity-0 {
    opacity: 0 !important
}

.opacity-50 {
    opacity: .5 !important
}

.opacity-75 {
    opacity: .75 !important
}

.overflow-hidden {
    overflow: hidden !important
}

.overflow-x-hidden {
    overflow-x: hidden !important
}

.d-inline {
    display: inline !important
}

.d-block {
    display: block !important
}

.d-grid {
    display: grid !important
}

.d-flex {
    display: flex !important
}

.d-none {
    display: none !important
}

.shadow-sm {
    box-shadow: var(--bs-box-shadow-sm) !important
}

.shadow-lg {
    box-shadow: var(--bs-box-shadow-lg) !important
}

.position-relative {
    position: relative !important
}

.position-absolute {
    position: absolute !important
}

.position-fixed {
    position: fixed !important
}

.position-sticky {
    position: sticky !important
}

.top-0 {
    top: 0 !important
}

.top-50 {
    top: 50% !important
}

.bottom-0 {
    bottom: 0 !important
}

.start-0 {
    right: 0 !important
}

.start-50 {
    right: 50% !important
}

.end-0 {
    left: 0 !important
}

.translate-middle-x {
    transform: translateX(50%) !important
}

.translate-middle-y {
    transform: translateY(-50%) !important
}

.border {
    border: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important
}

.border-0 {
    border: 0 !important
}

.border-top {
    border-top: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important
}

.border-top-0 {
    border-top: 0 !important
}

.border-bottom {
    border-bottom: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important
}

.border-2 {
    border-width: 2px !important
}

.w-50 {
    width: 50% !important
}

.w-100 {
    width: 100% !important
}

.w-auto {
    width: auto !important
}

.h-100 {
    height: 100% !important
}

.h-auto {
    height: auto !important
}

.flex-column {
    flex-direction: column !important
}

.flex-grow-1 {
    flex-grow: 1 !important
}

.flex-wrap {
    flex-wrap: wrap !important
}

.flex-nowrap {
    flex-wrap: nowrap !important
}

.justify-content-start {
    justify-content: flex-start !important
}

.justify-content-end {
    justify-content: flex-end !important
}

.justify-content-center {
    justify-content: center !important
}

.justify-content-between {
    justify-content: space-between !important
}

.align-items-start {
    align-items: flex-start !important
}

.align-items-center {
    align-items: center !important
}

.align-items-baseline {
    align-items: baseline !important
}

.order-2 {
    order: 2 !important
}

.m-0 {
    margin: 0 !important
}

.mx-1 {
    margin-right: .25rem !important;
    margin-left: .25rem !important
}

.mx-2 {
    margin-right: .5rem !important;
    margin-left: .5rem !important
}

.mx-3 {
    margin-right: 1rem !important;
    margin-left: 1rem !important
}

.mx-4 {
    margin-right: 1.5rem !important;
    margin-left: 1.5rem !important
}

.mx-auto {
    margin-right: auto !important;
    margin-left: auto !important
}

.my-1 {
    margin-bottom: .25rem !important;
    margin-top: .25rem !important
}

.my-2 {
    margin-bottom: .5rem !important;
    margin-top: .5rem !important
}

.my-3 {
    margin-bottom: 1rem !important;
    margin-top: 1rem !important
}

.my-4 {
    margin-bottom: 1.5rem !important;
    margin-top: 1.5rem !important
}

.mt-0 {
    margin-top: 0 !important
}

.mt-1 {
    margin-top: .25rem !important
}

.mt-2 {
    margin-top: .5rem !important
}

.mt-3 {
    margin-top: 1rem !important
}

.mt-4 {
    margin-top: 1.5rem !important
}

.mt-5 {
    margin-top: 3rem !important
}

.me-1 {
    margin-left: .25rem !important
}

.me-2 {
    margin-left: .5rem !important
}

.me-3 {
    margin-left: 1rem !important
}

.mb-0 {
    margin-bottom: 0 !important
}

.mb-1 {
    margin-bottom: .25rem !important
}

.mb-2 {
    margin-bottom: .5rem !important
}

.mb-3 {
    margin-bottom: 1rem !important
}

.mb-4 {
    margin-bottom: 1.5rem !important
}

.mb-5 {
    margin-bottom: 3rem !important
}

.ms-1 {
    margin-right: .25rem !important
}

.ms-2 {
    margin-right: .5rem !important
}

.ms-3 {
    margin-right: 1rem !important
}

.ms-5 {
    margin-right: 3rem !important
}

.p-0 {
    padding: 0 !important
}

.p-2 {
    padding: .5rem !important
}

.p-3 {
    padding: 1rem !important
}

.p-4 {
    padding: 1.5rem !important
}

.px-0 {
    padding-right: 0 !important;
    padding-left: 0 !important
}

.px-2 {
    padding-right: .5rem !important;
    padding-left: .5rem !important
}

.px-3 {
    padding-right: 1rem !important;
    padding-left: 1rem !important
}

.px-4 {
    padding-right: 1.5rem !important;
    padding-left: 1.5rem !important
}

.px-5 {
    padding-right: 3rem !important;
    padding-left: 3rem !important
}

.py-1 {
    padding-bottom: .25rem !important;
    padding-top: .25rem !important
}

.py-2 {
    padding-bottom: .5rem !important;
    padding-top: .5rem !important
}

.py-3 {
    padding-bottom: 1rem !important;
    padding-top: 1rem !important
}

.py-4 {
    padding-bottom: 1.5rem !important;
    padding-top: 1.5rem !important
}

.py-5 {
    padding-bottom: 3rem !important;
    padding-top: 3rem !important
}

.pt-0 {
    padding-top: 0 !important
}

.pt-1 {
    padding-top: .25rem !important
}

.pt-2 {
    padding-top: .5rem !important
}

.pt-3 {
    padding-top: 1rem !important
}

.pb-0 {
    padding-bottom: 0 !important
}

.pb-3 {
    padding-bottom: 1rem !important
}

.pb-4 {
    padding-bottom: 1.5rem !important
}

.pb-5 {
    padding-bottom: 3rem !important
}

.ps-1 {
    padding-right: .25rem !important
}

.ps-2 {
    padding-right: .5rem !important
}

.ps-3 {
    padding-right: 1rem !important
}

.ps-5 {
    padding-right: 3rem !important
}

.gap-1 {
    gap: .25rem !important
}

.gap-2 {
    gap: .5rem !important
}

.gap-3 {
    gap: 1rem !important
}

.gap-4 {
    gap: 1.5rem !important
}

.fs-1 {
    font-size: calc(1.3625rem + 1.35vw) !important
}

.fs-2 {
    font-size: calc(1.315rem + .78vw) !important
}

.fs-3 {
    font-size: calc(1.29125rem + .495vw) !important
}

.fs-4 {
    font-size: calc(1.2675rem + .21vw) !important
}

.fs-5 {
    font-size: 1.1875rem !important
}

.fs-6 {
    font-size: .95rem !important
}

.fst-italic {
    font-style: italic !important
}

.fst-normal {
    font-style: normal !important
}

.fw-normal {
    font-weight: 400 !important
}

.fw-medium {
    font-weight: 500 !important
}

.fw-semibold {
    font-weight: 600 !important
}

.fw-bold {
    font-weight: 700 !important
}

.fw-bolder {
    font-weight: bolder !important
}

.lh-sm {
    line-height: 1.25 !important
}

.text-start {
    text-align: right !important
}

.text-end {
    text-align: left !important
}

.text-center {
    text-align: center !important
}

.text-decoration-underline {
    text-decoration: underline !important
}

.text-uppercase {
    text-transform: uppercase !important
}

.text-capitalize {
    text-transform: capitalize !important
}

.text-wrap {
    white-space: normal !important
}

.text-nowrap {
    white-space: nowrap !important
}

.text-primary {
    --bs-text-opacity: 1;
    color: rgba(var(--bs-primary-rgb), var(--bs-text-opacity)) !important
}

.text-success {
    --bs-text-opacity: 1;
    color: rgba(var(--bs-success-rgb), var(--bs-text-opacity)) !important
}

.text-danger {
    --bs-text-opacity: 1;
    color: rgba(var(--bs-danger-rgb), var(--bs-text-opacity)) !important
}

.text-light {
    --bs-text-opacity: 1;
    color: rgba(var(--bs-light-rgb), var(--bs-text-opacity)) !important
}

.text-dark {
    --bs-text-opacity: 1;
    color: rgba(var(--bs-dark-rgb), var(--bs-text-opacity)) !important
}

.text-black {
    --bs-text-opacity: 1;
    color: rgba(var(--bs-black-rgb), var(--bs-text-opacity)) !important
}

.text-white {
    --bs-text-opacity: 1;
    color: rgba(var(--bs-white-rgb), var(--bs-text-opacity)) !important
}

.text-body {
    --bs-text-opacity: 1;
    color: rgba(var(--bs-body-color-rgb), var(--bs-text-opacity)) !important
}

.text-muted {
    --bs-text-opacity: 1;
    color: var(--bs-secondary-color) !important
}

.text-body-secondary {
    --bs-text-opacity: 1;
    color: var(--bs-secondary-color) !important
}

.bg-primary {
    --bs-bg-opacity: 1;
    background-color: rgba(var(--bs-primary-rgb), var(--bs-bg-opacity)) !important
}

.bg-secondary {
    --bs-bg-opacity: 1;
    background-color: rgba(var(--bs-secondary-rgb), var(--bs-bg-opacity)) !important
}

.bg-success {
    --bs-bg-opacity: 1;
    background-color: rgba(var(--bs-success-rgb), var(--bs-bg-opacity)) !important
}

.bg-info {
    --bs-bg-opacity: 1;
    background-color: rgba(var(--bs-info-rgb), var(--bs-bg-opacity)) !important
}

.bg-warning {
    --bs-bg-opacity: 1;
    background-color: rgba(var(--bs-warning-rgb), var(--bs-bg-opacity)) !important
}

.bg-danger {
    --bs-bg-opacity: 1;
    background-color: rgba(var(--bs-danger-rgb), var(--bs-bg-opacity)) !important
}

.bg-light {
    --bs-bg-opacity: 1;
    background-color: rgba(var(--bs-light-rgb), var(--bs-bg-opacity)) !important
}

.bg-dark {
    --bs-bg-opacity: 1;
    background-color: rgba(var(--bs-dark-rgb), var(--bs-bg-opacity)) !important
}

.bg-black {
    --bs-bg-opacity: 1;
    background-color: rgba(var(--bs-black-rgb), var(--bs-bg-opacity)) !important
}

.bg-white {
    --bs-bg-opacity: 1;
    background-color: rgba(var(--bs-white-rgb), var(--bs-bg-opacity)) !important
}

.bg-body {
    --bs-bg-opacity: 1;
    background-color: rgba(var(--bs-body-bg-rgb), var(--bs-bg-opacity)) !important
}

.bg-transparent {
    --bs-bg-opacity: 1;
    background-color: transparent !important
}

.bg-body-secondary {
    --bs-bg-opacity: 1;
    background-color: rgba(var(--bs-secondary-bg-rgb), var(--bs-bg-opacity)) !important
}

.bg-body-tertiary {
    --bs-bg-opacity: 1;
    background-color: rgba(var(--bs-tertiary-bg-rgb), var(--bs-bg-opacity)) !important
}

.bg-opacity-10 {
    --bs-bg-opacity: 0.1
}

.bg-opacity-25 {
    --bs-bg-opacity: 0.25
}

.bg-opacity-50 {
    --bs-bg-opacity: 0.5
}

.bg-opacity-75 {
    --bs-bg-opacity: 0.75
}

.bg-opacity-100 {
    --bs-bg-opacity: 1
}

.bg-primary-subtle {
    background-color: var(--bs-primary-bg-subtle) !important
}

.bg-secondary-subtle {
    background-color: var(--bs-secondary-bg-subtle) !important
}

.bg-success-subtle {
    background-color: var(--bs-success-bg-subtle) !important
}

.bg-info-subtle {
    background-color: var(--bs-info-bg-subtle) !important
}

.bg-warning-subtle {
    background-color: var(--bs-warning-bg-subtle) !important
}

.bg-danger-subtle {
    background-color: var(--bs-danger-bg-subtle) !important
}

.bg-light-subtle {
    background-color: var(--bs-light-bg-subtle) !important
}

.bg-dark-subtle {
    background-color: var(--bs-dark-bg-subtle) !important
}

.bg-gradient {
    background-image: var(--bs-gradient) !important
}

.rounded {
    border-radius: var(--bs-border-radius) !important
}

.rounded-0 {
    border-radius: 0 !important
}

.rounded-2 {
    border-radius: var(--bs-border-radius) !important
}

.rounded-3 {
    border-radius: var(--bs-border-radius-lg) !important
}

.rounded-4 {
    border-radius: var(--bs-border-radius-xl) !important
}

.rounded-pill {
    border-radius: var(--bs-border-radius-pill) !important
}

.rounded-top-0 {
    border-top-right-radius: 0 !important;
    border-top-left-radius: 0 !important
}

.rounded-top-2 {
    border-top-right-radius: var(--bs-border-radius) !important;
    border-top-left-radius: var(--bs-border-radius) !important
}

.rounded-end-1 {
    border-bottom-left-radius: var(--bs-border-radius-sm) !important;
    border-top-left-radius: var(--bs-border-radius-sm) !important
}

.rounded-bottom-0 {
    border-bottom-right-radius: 0 !important;
    border-bottom-left-radius: 0 !important
}

.rounded-start-0 {
    border-bottom-right-radius: 0 !important;
    border-top-right-radius: 0 !important
}

.visible {
    visibility: visible !important
}

.z-1 {
    z-index: 1 !important
}

.z-3 {
    z-index: 3 !important
}

@media(min-width:576px) {
    .d-sm-block {
        display: block !important
    }
    .mt-sm-0 {
        margin-top: 0 !important
    }
}

@media(min-width:768px) {
    .d-md-block {
        display: block !important
    }
    .d-md-none {
        display: none !important
    }
    .justify-content-md-center {
        justify-content: center !important
    }
    .px-md-4 {
        padding-right: 1.5rem !important;
        padding-left: 1.5rem !important
    }
    .px-md-5 {
        padding-right: 3rem !important;
        padding-left: 3rem !important
    }
}

@media(min-width:992px) {
    .d-lg-block {
        display: block !important
    }
    .d-lg-flex {
        display: flex !important
    }
    .d-lg-none {
        display: none !important
    }
    .flex-lg-row {
        flex-direction: row !important
    }
    .justify-content-lg-end {
        justify-content: flex-end !important
    }
    .order-lg-0 {
        order: 0 !important
    }
    .mx-lg-0 {
        margin-right: 0 !important;
        margin-left: 0 !important
    }
    .mt-lg-0 {
        margin-top: 0 !important
    }
    .px-lg-4 {
        padding-right: 1.5rem !important;
        padding-left: 1.5rem !important
    }
    .px-lg-5 {
        padding-right: 3rem !important;
        padding-left: 3rem !important
    }
    .py-lg-4 {
        padding-bottom: 1.5rem !important;
        padding-top: 1.5rem !important
    }
    .gap-lg-2 {
        gap: .5rem !important
    }
    .text-lg-start {
        text-align: right !important
    }
    .text-lg-end {
        text-align: left !important
    }
}

@media(min-width:1200px) {
    .fs-1 {
        font-size: 2.375rem !important
    }
    .fs-2 {
        font-size: 1.9rem !important
    }
    .fs-3 {
        font-size: 1.6625rem !important
    }
    .fs-4 {
        font-size: 1.425rem !important
    }
}