// function bin2hex(t) {
//     var a,
//         e,
//         o,
//         r = "";
//     for (t += "", a = 0, e = t.length; a < e; a++)
//         r += (o = t.charCodeAt(a).toString(16)).length < 2 ? "0" + o : o;
//     return r;
// }

function bin2hex(s) {
    var i;
    var l;
    var o = "";
    var n;

    s += "";

    for (i = 0, l = s.length; i < l; i++) {
        n = s.charCodeAt(i).toString(16);
        o += n.length < 2 ? "0" + n : n;
    }

    return o;
}

function bin2hexBase64Encode(str) {
    // const base64Encoded = btoa(str);
    // return Array.from(base64Encoded, (char) =>
    //     char.charCodeAt(0).toString(16).padStart(2, "0")
    // ).join("");
    // str = str.trim();
    return btoa(unescape(encodeURIComponent(str)));
}

function notifyUser(t, a = "success") {

    const $toastElement = $(`
       <div class="toast text-bg-light fade show" role="alert" data-bs-delay="3000" data-bs-autohide="true" aria-live="assertive" aria-atomic="true">
            <div class="toast-header text-bg-${a} justify-content-between">
                <span class="d-block"><i class="fa-regular fa-bell"></i></span>
                <button type="button" class="btn text-bg-${a} p-0" data-bs-dismiss="toast" aria-label="Close"><i class="fa-solid fa-xmark"></i></button>
            </div>
            <div class="toast-body">
                ${t}
            </div>
        </div>
    `);

    $(".toast-container.df-alert").append($toastElement);

    const toastInstance = new bootstrap.Toast($toastElement[0]);
    toastInstance.show();
}

var p = 1;

function loadMore(t) {
    var a = $("#formHb").serialize();
    $.ajax({
        url: base_url + "/ajax/loadMore",
        data: a,
        dataType: "html",
        type: "post",
        success: function(a) {
            "" == a
                ?
                $(t).remove() :
                (p++, $(".PageNum").val(p), $("#ImportLoadMore").append(a));
        },
    });
}

function changeQty(t, a) {
    $.ajax({
        url: base_url + "carts/change_qty",
        data: {
            id: t,
            qty: a
        },
        dataType: "html",
        type: "post",
        success: function(t) {
            loadCart(2);
            $(document).off("click", ".qtybutton");
        },
    });
}

function loadCartLast() {
    $.ajax({
        url: base_url + "carts/load_cart_last",
        data: {},
        dataType: "html",
        type: "post",
        success: function(t) {
            $(".cartLast").html(t);
        },
    });
}

function loadCart(t = 1) {
    $.ajax({
        url: base_url + "carts/load_cart",
        data: {},
        dataType: "html",
        success: function(a) {
            loadCartNum(), $(".minicart").html(a), 2 == t && loadCartLast();
        },
    });
}

function loadCartNum() {
    $.ajax({
        url: base_url + "carts/load_cart_num",
        data: {},
        dataType: "JSON",
        success: function(t) {
            if (t.n > 0) {
                $(".cart-count").css("display", "block");
                $(".cart-count").html(t.n);
            } else {
                $(".cart-count").css("display", "none");
            }
            $(".cart-price").html(
                    "<b>" + t.p.toFixed(2) + "</b>" + " " + t.currency
                ),
                t.n > 0 ?
                $(".cart-total-price").show() :
                $(".cart-total-price").hide();
        },
    });
}

function addToCart(t, a = this, e = 1) {
    if (1 == e) var o = $("#changeQty").val();
    else if (2 == e) var o = $(a).parent().prev().find("input").val();
    else var o = 1;
    var r = $("#Form-Variants").serializeArray();
    let variantData = {};
    if (r.length == 0) {
        r = $("#variant-selection-container").serializeArray();
        if (r && r.length > 0) {
            variantData = JSON.parse(r[0].value);
        }
    } else {
        variantData = r;
    }
    let selecteOffer = $(".selected-offer");
    if (selecteOffer.length) {
        o = selecteOffer
            .find('input[name="selected_offer"]')
            .attr("data-offer-qty");
    }

    $.ajax({
        url: base_url + "carts",
        data: {
            id: t,
            qty: o ? ? 1,
            options: variantData,
            offer: o
        },
        dataType: "json",
        success: function(response) {
            response.status == true ?
                (loadCart(),
                    notifyUser(add_to_cart_message),
                    eventAddToCart(t, o ? ? 1, response.data),
                    $(".cart-count").css("display", "block"),
                    $(".cart-total-price").removeClass("d-none")) :
                alert(somethingWentWrong);
            $(".gocheckout-button").removeClass("d-none");
            document.querySelector('[data-bs-target="#cartSummaryOffcanvas"]') ? .click();
        },
    });
}

document.addEventListener("DOMContentLoaded", function() {
    // Select all anchor tags with the ref 'variantPicker'
    var links = document.querySelectorAll('[ref="variantPicker"]');

    links.forEach(function(link) {
        link.addEventListener("click", function(event) {
            event.preventDefault(); // Prevent the default action of the anchor tag

            // Find the parent form
            var parentForm = this.closest(".rollUpQuickBuyWrap");

            // Remove 'variant-checked' class from all links inside the parent form
            var allVariantLinks = parentForm.querySelectorAll(
                '[ref="variantPicker"]'
            );
            allVariantLinks.forEach(function(variantLink) {
                variantLink.classList.remove("variant-checked");
                var input = variantLink.querySelector('input[type="radio"]');
                if (input) {
                    input.checked = false; // Uncheck the radio input
                }
            });

            // Check the current radio input and add 'variant-checked' class
            var radioInput = this.querySelector('input[type="radio"]');
            if (radioInput) {
                radioInput.checked = true; // Set the radio input as checked
                // Add the 'variant-checked' class to the current link
                this.classList.add("variant-checked");
            }
        });
    });
});

function advancedAddToCart(e, that, id) {
    e.preventDefault();
    ref = "card-wrap";

    var cardWrap = $(that).closest('[ref="card-wrap"]');
    var form = cardWrap.find('[ref="variantForm"]');

    var formData = form.serializeArray();
    var variants = JSON.stringify(formData);
    var o = null;
    var o = 1;

    $.ajax({
        url: base_url + "carts",
        data: {
            id: id,
            qty: o ? ? 1,
            options: JSON.parse(variants),
            offer: o
        },
        dataType: "json",
        success: function(t) {
            true == t.status ?
                (
                    loadCart(),
                    notifyUser(add_to_cart_message),
                    eventAddToCart(),
                    $(".cart-count").css("display", "block"),
                    $(".cart-total-price").removeClass("d-none")) :
                alert(somethingWentWrong);
            $(".gocheckout-button").removeClass("d-none");
            document.querySelector('[data-bs-target="#cartSummaryOffcanvas"]') ? .click();
        },
    });
}

// attach event to change of variantForm form elements
// document.addEventListener("DOMContentLoaded", function () {
//     var variantForm = document.querySelector('[ref="variantForm"]');

//     // Listen for clicks on the color anchor tags
//     var colorPickers = variantForm.querySelectorAll('[ref="variantPicker"]');
//     colorPickers.forEach(function(picker) {
//         picker.addEventListener("click", function(e) {
//             e.preventDefault(); // Prevent anchor tag navigation

//             // Find the associated radio input and check it
//             const radio = this.querySelector('input[type="radio"]');
//             radio.checked = true;

//             // Trigger the change event manually
//             const event = new Event('change');
//             radio.dispatchEvent(event);
//         });
//     });

//     // Keep the existing radio change listeners
//     let allRadios = variantForm.querySelectorAll('input[type="radio"]');
//     allRadios.forEach(function (radio) {
//         radio.addEventListener("change", function () {
//             var formData = new FormData(variantForm);
//             var values = Array.from(formData.values()).join("-");
//             var productId = variantForm.dataset.id;

//             console.log(values, productId);

//             var hexEncoded = bin2hex(values);
//             var hexBase64Encoded = bin2hexBase64Encode(values);

//             console.log(_grouped_variants, productId, hexEncoded, hexBase64Encoded);
//             var matchingVariant = _grouped_variants[productId]?.find(
//                 (variant) =>
//                     variant.combination === hexEncoded ||
//                     variant.combination === hexBase64Encoded
//             );

//             console.log(matchingVariant);

//             if (matchingVariant) {
//                 var newPriceEl = variantForm.closest('[ref="product-card"]').querySelector('[ref="product-new-price"]');
//                 var oldPriceEl = variantForm.closest('[ref="product-card"]').querySelector('[ref="product-old-price"]');

//                 if (newPriceEl) newPriceEl.textContent = erGetPrice(matchingVariant.price);
//                 if (oldPriceEl) oldPriceEl.textContent = erGetPrice(matchingVariant.compare_price);
//             }
//         });
//     });
// });

function toggleWishlistItem(id) {

    $(`.product-card[data-id="${id}"]`).each((_, item) => {
        $(item) ? .find(".addToFavorite i") ? .toggleClass('text-danger');
    });
}

function addToFavorite(t, a, e = 1) {
    let o = $(t);
    let wishlistCount = parseInt($(".wishlist-count").html() ? ? "0");
    $.ajax({
        url: base_url + "favorites",
        data: {
            id: a
        },
        dataType: "JSON",
        type: "post",
        success: function(t) {
            switch (t) {
                case 1:
                    if (wishlistCount == 0) $(".wishlist-count").show();

                    toggleWishlistItem(a);

                    $(".wishlist-count").html(wishlistCount + 1);
                    notifyUser(add_to_favorit_message);
                    break;
                case 0:
                    if (e == 2) {
                        o.parent().parent().remove();
                        $(".wishlist-count").html(wishlistCount - 1);
                        if (wishlistCount == 1) $(".wishlist-count").hide();
                    } else {
                        toggleWishlistItem(a);

                        $(".wishlist-count").html(wishlistCount - 1);
                        if (wishlistCount == 1) $(".wishlist-count").hide();
                    }
                    break;
                default:
                    alert(somethingWentWrong);
                    break;
            }
        },
    });
}

function deleteFromCart(t, a, e = 1) {
    var o = $(a);
    return (
        $.ajax({
            url: base_url + "carts/delete_from_cart",
            data: {
                id: t
            },
            dataType: "html",
            type: "post",
            success: function(t) {
                1 == t ?
                    (2 == e && o.parent().parent().remove(),
                        loadCart(),
                        notifyUser(remove_to_cart_message, "danger")) :
                    alert(somethingWentWrong);
                let event = $(
                    "#customer_infos_payment_providers_containe"
                ).data("event");
                if (event == "strabl") {
                    document.querySelector("#strabl-checkout-btn").click();
                }
            },
        }), !1
    );
}

function editProductQty(e, id, qty) {
    e.preventDefault();
    var $itemCardBody = $(e.target).closest(".item-card-body");
    var qty_text = $itemCardBody.find(".qty-text").text();
    $itemCardBody.find(".qty-value").html(`
        <span class="summary-update-qty">
            <span class="qty-text">${qty_text}</span>
            <input type="number" id="inpt-qty-${id}" ref="quantity" value="${qty}">
            <button class="summary-submit-qty" data-id="${id}">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512">
                    <path d="M448 130L431 147 177.5 399.2l-16.9 16.9-16.9-16.9L17 273.1 0 256.2l33.9-34 17 16.9L160.6 348.3 397.1 112.9l17-16.9L448 130z"/>
                </svg>
            </button>
        </span>`);
    $(`#inpt-qty-${id}`).on("change", e => {
        if ($(e.target).val() < 1)
            $(e.target).val(1);
    });
}

$(document).on("click", ".summary-submit-qty", function(e) {
    e.preventDefault();
    var $itemCardBody = $(this).closest(".item-card-body");
    var id = $(this).data("id");
    var qty = $itemCardBody.find('input[ref="quantity"]').val();
    changeQty(id, qty);
});

function validateCoupon(t, a) {
    return t.is(":visible") && "" === t.val() && "hidden" != t.attr("type") ?
        ($(a).show(), t.css("border-color", "red"), !1) :
        (t.css("border-color", "#e6e6e6"), $(a).hide(), !0);
}

function applyCoupon() {
    var couponCodeInput = $("#couponCode");

    if (!validateCoupon(couponCodeInput, ".errorForbiddenKeyword_coupon")) {
        return false;
    }

    var couponCode = couponCodeInput.val();
    var selectedPaymentMethod = $('input[name="payment_method"]:checked').attr(
        "data-payment-type"
    );

    $.ajax({
        url: base_url + "checkout/apply_coupon",
        data: {
            coupon: couponCode,
            selectedPaymentMethod: selectedPaymentMethod,
        },
        dataType: "JSON",
        type: "post",
        success: function(response) {
            if (response.status) {
                handleCouponSuccess(response, couponCode);
                let event = $(
                    "#customer_infos_payment_providers_containe"
                ).data("event");
                if (event == "strabl") {
                    document.querySelector("#strabl-checkout-btn").click();
                }
            } else {
                notifyUser(response.msg, "danger");
            }
        },
    });

    return false;
}

function handleCouponSuccess(response, couponCode) {
    let shippingCost =
        parseFloat(
            $('input[name="shipping"]:checked').data("localprice") || $('select[name="shipping"] option:selected').data("localprice")
        ) || 0;

    var newTotal = parseFloat(response.newTotal);
    var couponDiscount = parseFloat(response.couponDiscount).toFixed(2);

    $(".coupon-applied .text-coupon").text(`${discountTxt} (${couponCode})`);
    $(".coupon-applied .total_discount").text(couponDiscount);
    $(".coupon-applied").show();
    $(".total_order_2").text((shippingCost + newTotal).toFixed(2));
}

// function updatePaymentDiscount(paymentDiscountElement) {
//     let paymentDiscountValue = paymentDiscountElement.data("payment-discount");

//     if (paymentDiscountValue !== 0)
//         return;
//     var discountAmount = 0;

//     if (paymentDiscountElement.is(":visible")) {
//         discountAmount = Math.floor((100.99 * paymentDiscountValue) / 100 * 100) / 100;

//         if (paymentDiscountValue !== 0) {
//             paymentDiscountElement.text(`${discountAmount} (${paymentDiscountValue} %)`);
//         } else {
//             $(".payment_discount_amount").hide();
//         }
//     }
// }

function goNext(t) {
    return $(".step").hide(), $("#step" + t).fadeIn("slow"), !1;
}

$(document).on("change", "#changeQty", function() {
    var t = $(this).val(),
        a = $(this).data("price");
    $(".qtySelected").text(t), $(".totalPrice").text((t * a).toFixed(2));
});

const dropifyPlugin = (() => {
    const defaultConfig = {
        variantPickerSelector: '[ref="variantPicker"]',
        variantValueSelector: '[ref="variant_val"]',
        variantFormSelector: 'form[ref="variantForm"]',
        productCardSelector: '[ref="product-card"]',
        productNewPriceSelector: '[ref="product-new-price"]',
        productOldPriceSelector: '[ref="product-old-price"]',
        groupedVariants: {},
    };
    let config = { ...defaultConfig
    };

    function erGetPrice(price) {
        let _total_converted_price =
            (price / (_er_rate1 > 0 ? _er_rate1 : 1)) * _er_rate2;
        let _visitorPrice =
            _multi_currency_is_active && _rounded === 1 ?
            _total_converted_price > 0 ?
            Math.ceil(_total_converted_price) - 0.01 :
            0 :
            _total_converted_price;
        return (formattedVisitorPrice = _visitorPrice.toFixed(2));
    }

    function initVariantesInCardsWidget(element) {
        const form = element.closest(config.variantFormSelector);
        const formData = new FormData(form);
        const values = Array.from(formData.values()).join("-");
        const productId = form.dataset.id;

        const hexEncoded = bin2hex(values);
        const hexBase64Encoded = bin2hex(bin2hexBase64Encode(values));

        const matchingVariant = config.groupedVariants[productId] ? .find(
            (variant) =>
            variant.combination === hexEncoded ||
            variant.combination === hexBase64Encoded
        );

        if (matchingVariant) {
            const productCard = form.closest('[ref="product-card"]');
            if (productCard) {
                const newPriceEl = productCard.querySelector(
                    '[ref="product-new-price"]'
                );
                const oldPriceEl = productCard.querySelector(
                    '[ref="product-old-price"]'
                );
                if (newPriceEl)
                    newPriceEl.textContent = erGetPrice(matchingVariant.price);
                if (oldPriceEl)
                    oldPriceEl.textContent = erGetPrice(
                        matchingVariant.compare_price
                    );
            }
        }
    }

    function formatOfferPrices(offers) {
        if (offers.length) {
            offers.forEach((offer) => {
                if (offer.price)
                    offer.price = parseFloat(offer.price).toFixed(2);
                if (offer.compare_price)
                    offer.compare_price = parseFloat(
                        offer.compare_price
                    ).toFixed(2);
            });
        }
    }

    function createOfferListManager(
        offerListId,
        offerNewRef,
        offerOldRef,
        offers
    ) {
        return {
            init: function() {
                const offerList = document.getElementById(offerListId);
                const offerNewElement = document.querySelector(
                    `span[ref="${offerNewRef}"]`
                );
                const offerOldElement = document.querySelector(
                    `span[ref="${offerOldRef}"]`
                );

                if (offerList && offerNewElement && offerOldElement) {
                    offerList.addEventListener("change", (event) => {
                        if (event.target.name === "offers") {
                            const selectedIndex = event.target.value;
                            const selectedOffer = offers.find(
                                (offer) => offer.qty === parseInt(selectedIndex)
                            );
                            if (selectedOffer) {
                                offerNewElement.innerHTML = selectedOffer.price;
                                offerOldElement.innerHTML =
                                    selectedOffer.compare_price;
                            }
                        }
                    });
                }
            },
        };
    }

    function createVariantManager() {
        return {
            init: function() {
                document.addEventListener("DOMContentLoaded", function() {
                    // Attach event listeners to all variant links
                    document
                        .querySelectorAll(config.variantPickerSelector)
                        .forEach((variantLink) => {
                            variantLink.addEventListener("click", function(e) {
                                e.preventDefault(); // Prevent default anchor behavior
                                initVariantesInCardsWidget(e.currentTarget); // Pass the clicked element
                            });
                        });

                    document.querySelectorAll('.card-variant-wrap').forEach((product) => {
                        const firstVariant = product.querySelector(config.variantPickerSelector);
                        if (firstVariant) {
                            firstVariant.click();
                        }
                    });
                });
            },
        };
    }

    return {
        setConfig(customConfig = {}) {
            config = { ...config,
                ...customConfig
            };
        },
        createOfferListManager,
        createVariantManager,
        formatOfferPrices,
    };
})();

document.addEventListener("DOMContentLoaded", function() {
    dropifyPlugin.setConfig({
        groupedVariants: typeof _grouped_variants !== "undefined" ? _grouped_variants : {},
    });
});

const variantManager = dropifyPlugin.createVariantManager();
variantManager.init();