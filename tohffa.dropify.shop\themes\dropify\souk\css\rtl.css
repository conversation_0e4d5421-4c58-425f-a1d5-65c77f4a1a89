body {
    margin: 0;
    font-size: 1rem !important;
    font-weight: 500 !important;
    line-height: 1.5;
    text-align: right;
}

.menu-area .mobile-nav-toggler {
    margin-right: 18px;
    margin-left: 0px;
    float: left !important;
}

.mobile-menu .navigation li.dropdown .dropdown-btn {
    left: 10px !important;
    right: auto;
}

.shop-check-wrap {
    padding-right: 1.3rem !important;
}

.header-category>a i:first-child {
    margin-left: 18px !important;
    margin-right: 0px;
}

.header-category>a i:last-child {
    margin-right: 15px !important;
}

.category-menu {
    right: 0 !important;
}

.mega-menu {
    right: 100% !important;
}

.category-menu>li.has-dropdown>a::after {
    content: "\f104";
}

.header-shop-cart a span.cart-count {
    right: 13px !important;
    bottom: -2px !important;
}

.header-shop-cart a span.wishlist-count {
    right: 13px !important;
    bottom: -2px !important;
}

.header-top-left ul li:last-child,
.header-top-right ul li:last-child {
    margin-left: 0;
    padding-left: 0;
}

.dropdown-toggle::after {
    margin-right: 0.255em;
}

.header-shop-cart ul.minicart {
    left: 0 !important;
    right: auto;
}

.header-action-area .header-action .mini-cart-dropdown .cart-total-money {
    text-align: right;
}

.header-action-area .header-action .mini-cart-dropdown .cart-total-money h5 .money {
    float: left;
}

.header-shop-cart .minicart>li {
    margin-right: 0 !important;
}

.header-shop-cart ul.minicart::before {
    right: 0px !important;
    left: inherit !important;
}

.header-shop-cart .minicart .cart-content {
    padding-right: 15px !important;
    text-align: right !important;
    padding-left: 25px !important;
}

.header-shop-cart .cart-total-price {
    margin-right: 15px !important;
}

.header-action ul li:first-child {
    margin-right: 0 !important;
}

.header-action>ul {
    margin-right: 25px !important;
    margin-left: 0px !important;
}

.header-action>ul li {
    margin-left: 0px !important;
    margin-right: 25px;
}

.header-search-wrap form button {
    border-radius: 5px 0px 0px 5px;
}

.header-action>ul>li.header-shop-cart>a {
    padding-left: 10px;
    padding-right: 0px;
}

.shop-sidebar {
    margin-left: 25px;
    margin-right: 0px;
}

.shop-cat-list ul li a {
    float: right !important;
    padding-right: 20px;
    padding-left: 20px;
}

.pagination-wrap ul li.next {
    margin-right: 20px !important;
    margin-left: 0px !important;
}

.pagination-wrap ul li.prev {
    margin-left: 20px;
    margin-right: 0px !important;
}

.exclusive--content--top .old-price {
    text-align: left !important;
}

.shop-widget-title .slider-nav {
    left: 0 !important;
    right: inherit;
}

.shop-cat-list ul li span {
    float: left !important;
}

.shop-cat-list ul li a::before {
    right: 0;
    float: inherit;
}

.footer-text p {
    padding-left: 50px !important;
    padding-right: 0px;
}

.footer-contact ul li i {
    margin-left: 10px;
    margin-right: 0px;
}

.super-deal-title h3 {
    margin-left: 15px !important;
    margin-right: 0px !important;
}

.footer-alphabet span {
    margin-left: 15px;
    margin-right: 0px !important;
}

.footer-social ul li {
    margin-left: 40px;
    margin-right: 0px;
}

.footer-newsletter-wrap .newsletter-form form input {
    padding-right: 10px;
    padding-left: 0px;
}

.footer-newsletter-wrap .newsletter-form form button {
    left: 0 !important;
    right: inherit;
    border-radius: 5px 0px 0px 5px
}

.dropdown-menu .show {
    right: 0 !important;
}

.dropdown-item {
    text-align: right;
}

.header-search-wrap form input {
    border-radius: 0px 5px 5px 0px !important;
}

.header-top-left .dropdown img {
    margin-right: 0px;
    margin-left: 5px;
}

/*  Product Page */

.shop-details-nav-wrap {
    float: right !important;
}

.shop-details-img-wrap {
    margin-right: 115px !important;
    margin-left: 50px !important;
}

.perched-info .cart-plus {
    margin-right: 0px;
    margin-left: 15px;
}

.textual-button label {
    margin-left: 5px !important;
    margin-right: 0px !important;
}

.shop-details-content {
    margin-left: 0px !important;
    margin-right: 0px !important;
}

.shop-details-review .rating {
    margin-left: 10px !important;
    margin-right: 0px !important;
}

.shop-details-bottom h5 a i {
    margin-left: 7px !important;
    margin-right: 0px !important;
}

.comment-avatar-img {
    margin-left: 25px;
    margin-right: 0px;
}

.product-review-list .rating {
    float: left !important;
}

.comment-avatar-info h5 span {
    margin-right: 10px !important;
    margin-left: 0px;
}

.newsletter-title h4 {
    margin-bottom: 15px !important;
}

.header-top-left .dropdown button {
    padding: 0px !important;
}

.breadcrumb {
    font-size: 14px !important;
    font-weight: 500 !important;
}

.sidebar-product-content {
    text-align: right !important;
}

.sidebar-product-content h5 {
    line-height: 25px !important;
}

.slider-nav {
    direction: ltr !important;
}

.sidebar-product-thumb {
    margin-left: 18px;
    margin-right: 0px;
}

.slick-prev .slick-arrow {
    margin-right: 20px !important;
}

.core-features-item::after {
    left: -15px;
    right: inherit;
}

.paypal-method p::before {
    right: 0;
    left: inherit;
}

.paypal-method p {
    margin-right: 0px;
    margin-left: 0px;
    padding-right: 10px;
    padding-left: 0px;
}

.custom-control-label::before {
    right: -1.5rem !important;
    left: inherit;
}

.custom-control-label::after {
    right: -1.5rem !important;
    left: inherit;
}

.custom-control-input {
    right: 0;
    left: inherit;
}

.shop-cart-area .table thead th:first-child {
    border-right: none !important;
    border-left: none !important;
}

.shop-cart-area .table thead th:last-child {
    border-right: none !important;
    border-left: none !important;
}

.shop-cart-area .table td:first-child {
    border-right: 1px solid #b6b6b652;
    border-left: none;
}

.shop-cart-area .table td:last-child {
    border-left: 1px solid #b6b6b652;
    border-right: none;
}

.wishlist-area tbody .product-name span::before {
    margin-left: 10px !important;
    margin-right: 0px !important;
}

.no-resuls {
    font-size: 14.5px;
}

.total-price {
    font-size: 14px !important;
}

.shop-cart-widget .title {
    padding-bottom: 25px !important;
}

.header-shop-cart .minicart .checkout-link a {
    letter-spacing: 0px !important;
}

.comment-check-box input {
    margin: 4px 0px 0px 5px !important;
}

.popup-title .title {
    font-size: 20px !important;
    line-height: 33px !important;
}

.box-shadow-m-up {
    background-color: #fff;
    border-top: 1px solid #f0f0f0;
    padding: 10px 0;
    box-shadow: 0 -10px 20px -20px rgb(0 0 0 / 10%);
}

.navbar-wrap ul {
    margin-right: auto;
    margin-left: inherit;
}

.menu-wrap .navbar-wrap {
    margin: auto;
}

.navbar-wrap>ul>li:last-child a {
    padding-right: 20px;
    padding-left: 0;
}

@media(max-width:768px) {
    .header-shop-cart ul.minicart {
        left: auto !important;
        right: -57px !important;
    }
}

.close {
    left: 8px;
    right: auto !important;
    top: 12px !important;
}

.iti__country {
    text-align: right;
}

.checkout-form .form-grp .custom-select {
    padding: 7px 16px 10px 16px;
    background: no-repeat scroll 0;
}

.iti__arrow {
    margin-right: 6px;
}

.mobile-menu .nav-logo {
    text-align: right;
}

.mobile-menu .close-btn {
    left: 30px;
    right: auto;
}

.shop-meta-right form {
    margin-left: 0;
    margin-right: 10px;
}

@media(max-width:768px) {
    .shop-sidebar {
        margin-left: 0px !important;
    }
}

.radio-variant {
    margin-left: 8px !important;
}

.exclusive-item-three {
    margin-left: auto;
    margin-right: 25px;
}

.navbar-wrap ul li .submenu {
    left: 0 !important;
    right: auto;
}

.navbar-wrap ul li .submenu li {
    text-align: right;
}

.custom-control {
    padding-right: 1.5rem;
}