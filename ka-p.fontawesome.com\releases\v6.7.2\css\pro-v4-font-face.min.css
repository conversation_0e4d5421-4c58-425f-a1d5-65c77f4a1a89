/*!
 * Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com
 * License - https://fontawesome.com/license (Commercial License)
 * Copyright 2024 Fonticons, Inc.
 */

@font-face {
    font-family: "FontAwesome";
    font-display: block;
    src: url(../webfonts/pro-fa-solid-900-0.woff2) format("woff2"), url(../webfonts/pro-fa-solid-900-0.ttf) format("truetype");
    unicode-range: u+f001, u+f004-f005, u+f007-f008, u+f00c, u+f011-f012, u+f015, u+f018-f019, u+f01c, u+f023-f025, u+f02a, u+f02c-f031, u+f03a, u+f03d, u+f04a-f04e, u+f05b, u+f060-f064, u+f067-f068, u+f06b-f06d, u+f072, u+f075, u+f077-f078, u+f07b, u+f084, u+f086, u+f091, u+f093, u+f095, u+f09c, u+f0a3, u+f0ad, u+f0b0-f0b1, u+f0c0-f0c2, u+f0c6, u+f0c8, u+f128, u+f12a, u+f155, u+f283, u+f292, u+f295
}

@font-face {
    font-family: "FontAwesome";
    font-display: block;
    src: url(../webfonts/pro-fa-solid-900-1.woff2) format("woff2"), url(../webfonts/pro-fa-solid-900-1.ttf) format("truetype");
    unicode-range: u+f040, u+f0c9, u+f0cc, u+f0ce, u+f0d1, u+f0d7, u+f0dc, u+f0e0, u+f0e7-f0e8, u+f0f3, u+f106, u+f108-f109, u+f10e, u+f110-f112, u+f11e, u+f121, u+f126, u+f129, u+f12c-f12e, u+f130-f132, u+f135, u+f13d, u+f140, u+f145, u+f15b, u+f164, u+f188, u+f1ab, u+f1ad-f1ae, u+f1b2, u+f1b8, u+f1bb, u+f1c0, u+f1ce, u+f1d8, u+f1dc, u+f1e4-f1e6, u+f1eb-f1ec, u+f205, u+f217, u+f21a-f21d, u+f22d, u+f233-f234, u+f238, u+f246, u+f251, u+f275, u+f29e, u+f2a0, u+f2a7, u+f2bb, u+f2cc-f2cd, u+f2db
}

@font-face {
    font-family: "FontAwesome";
    font-display: block;
    src: url(../webfonts/pro-fa-solid-900-3.woff2) format("woff2"), url(../webfonts/pro-fa-solid-900-3.ttf) format("truetype");
    unicode-range: u+f069, u+f1fa
}

@font-face {
    font-family: "FontAwesome";
    font-display: block;
    src: url(../webfonts/pro-fa-solid-900-12.woff2) format("woff2"), url(../webfonts/pro-fa-solid-900-12.ttf) format("truetype");
    unicode-range: u+f000, u+f002, u+f009-f00b, u+f00d-f00e, u+f010, u+f013, u+f01e, u+f021, u+f026-f029, u+f02b, u+f032-f039, u+f03b-f03c, u+f042-f043, u+f048-f049, u+f050-f05a, u+f05e, u+f06a, u+f071, u+f073-f074, u+f076, u+f079-f07a, u+f07c, u+f083, u+f085, u+f089, u+f08d, u+f098, u+f09e, u+f0a1
}

@font-face {
    font-family: "FontAwesome";
    font-display: block;
    src: url(../webfonts/pro-fa-solid-900-13.woff2) format("woff2"), url(../webfonts/pro-fa-solid-900-13.ttf) format("truetype");
    unicode-range: u+f045, u+f0a8-f0ab, u+f0c3-f0c4, u+f0ca-f0cb, u+f0cd, u+f0d8-f0db, u+f0dd-f0de, u+f0e2-f0e3, u+f0e9-f0ea, u+f0ed-f0ee, u+f0f0-f0f2, u+f0f4, u+f0f9-f0fe, u+f100-f105, u+f107, u+f10d, u+f11b, u+f120, u+f122, u+f124-f125, u+f127, u+f12b, u+f134, u+f137-f13a, u+f141-f144, u+f146, u+f14a-f14b, u+f14d, u+f153-f154, u+f157-f159, u+f15c-f15d, u+f162, u+f165, u+f182-f183, u+f187, u+f193, u+f197, u+f199, u+f19c-f19d, u+f1ac, u+f1b0, u+f1b3, u+f1b9-f1ba, u+f1cd, u+f1da, u+f1dd-f1de, u+f1e0-f1e2
}

@font-face {
    font-family: "FontAwesome";
    font-display: block;
    src: url(../webfonts/pro-fa-solid-900-14.woff2) format("woff2"), url(../webfonts/pro-fa-solid-900-14.ttf) format("truetype");
    unicode-range: u+f0f5, u+f1b1, u+f1f6, u+f1fb-f1fe, u+f200-f201, u+f204, u+f206-f207, u+f20b, u+f218, u+f21e, u+f221-f22c, u+f235-f236, u+f239, u+f240-f245, u+f249, u+f24e, u+f250, u+f252-f254, u+f25c, u+f26c, u+f276-f277, u+f279, u+f28b, u+f28d, u+f290-f291, u+f29a, u+f29d, u+f2a1-f2a4, u+f2a8, u+f2b6, u+f2b9, u+f2bd, u+f2c2, u+f2c7-f2cb, u+f2ce, u+f2d1, u+f2d3
}

@font-face {
    font-family: "FontAwesome";
    font-display: block;
    src: url(../webfonts/pro-fa-brands-400-0.woff2) format("woff2"), url(../webfonts/pro-fa-brands-400-0.ttf) format("truetype");
    unicode-range: u+f081-f082, u+f08c, u+f092, u+f099, u+f09b, u+f0d2-f0d5, u+f0e1, u+f113, u+f136, u+f13b-f13c, u+f15a, u+f167-f16e, u+f170-f174, u+f179-f17e, u+f180-f181, u+f184, u+f189-f18d, u+f194, u+f198, u+f19a-f19b, u+f19e, u+f1a0-f1a4, u+f1b4, u+f1bc, u+f1be, u+f1e8, u+f1ed, u+f1f0-f1f1, u+f20e, u+f210, u+f213-f214, u+f230, u+f232, u+f23a, u+f26b, u+f270, u+f288, u+f299, u+f29b, u+f2a6, u+f2b0, u+f2c5-f2c6, u+f2e0
}

@font-face {
    font-family: "FontAwesome";
    font-display: block;
    src: url(../webfonts/pro-fa-brands-400-1.woff2) format("woff2"), url(../webfonts/pro-fa-brands-400-1.ttf) format("truetype");
    unicode-range: u+f1a5-f1aa, u+f1b5-f1b7, u+f1bd, u+f1ca-f1cc, u+f1d0-f1d7, u+f1e7, u+f1e9, u+f1ee, u+f1f2-f1f5, u+f202-f203, u+f208-f209, u+f20d, u+f211-f212, u+f215-f216, u+f231, u+f237, u+f23b-f23e, u+f24b-f24c, u+f25e, u+f260-f261, u+f263-f26a, u+f26d-f26e, u+f27c-f27e, u+f280-f282, u+f284-f287, u+f289-f28a, u+f293-f294, u+f296-f298, u+f2a5, u+f2a9-f2ae, u+f2b1-f2b4, u+f2b8, u+f2c4, u+f2d5-f2da, u+f2dd-f2de
}

@font-face {
    font-family: "FontAwesome";
    font-display: block;
    src: url(../webfonts/pro-fa-brands-400-2.woff2) format("woff2"), url(../webfonts/pro-fa-brands-400-2.ttf) format("truetype");
    unicode-range: u+f166
}

@font-face {
    font-family: "FontAwesome";
    font-display: block;
    src: url(../webfonts/pro-fa-regular-400-0.woff2) format("woff2"), url(../webfonts/pro-fa-regular-400-0.ttf) format("truetype");
    unicode-range: u+f006, u+f017, u+f03e, u+f06e, u+f08a, u+f096-f097, u+f09d, u+f0a6, u+f0c5, u+f0e5-f0e6, u+f114, u+f11d, u+f2c0
}

@font-face {
    font-family: "FontAwesome";
    font-display: block;
    src: url(../webfonts/pro-fa-regular-400-1.woff2) format("woff2"), url(../webfonts/pro-fa-regular-400-1.ttf) format("truetype");
    unicode-range: u+f003, u+f016, u+f087, u+f0a2, u+f0eb, u+f0f7-f0f8, u+f10c, u+f11c, u+f133, u+f14e, u+f186, u+f1c1-f1c3, u+f1d9, u+f1db, u+f1ea, u+f1f9, u+f20a, u+f24d, u+f25d, u+f2b5, u+f2bc, u+f2d2
}

@font-face {
    font-family: "FontAwesome";
    font-display: block;
    src: url(../webfonts/pro-fa-regular-400-12.woff2) format("woff2"), url(../webfonts/pro-fa-regular-400-12.ttf) format("truetype");
    unicode-range: u+f022, u+f044, u+f05c-f05d, u+f070, u+f094, u+f0a0, u+f115, u+f29c
}

@font-face {
    font-family: "FontAwesome";
    font-display: block;
    src: url(../webfonts/pro-fa-regular-400-13.woff2) format("woff2"), url(../webfonts/pro-fa-regular-400-13.ttf) format("truetype");
    unicode-range: u+f01d, u+f046, u+f088, u+f0a4-f0a5, u+f0a7, u+f0c7, u+f0f6, u+f118-f11a, u+f147, u+f150-f152, u+f185, u+f191-f192, u+f196, u+f1c4-f1c9, u+f1e3
}

@font-face {
    font-family: "FontAwesome";
    font-display: block;
    src: url(../webfonts/pro-fa-regular-400-14.woff2) format("woff2"), url(../webfonts/pro-fa-regular-400-14.ttf) format("truetype");
    unicode-range: u+f014, u+f1f7, u+f247-f248, u+f24a, u+f255-f25b, u+f271-f274, u+f278, u+f28c, u+f28e, u+f2b7, u+f2ba, u+f2be, u+f2c1, u+f2c3, u+f2d0, u+f2dc
}

@font-face {
    font-family: "FontAwesome";
    font-display: block;
    src: url(../webfonts/pro-fa-regular-400-15.woff2) format("woff2"), url(../webfonts/pro-fa-regular-400-15.ttf) format("truetype");
    unicode-range: u+f01a-f01b, u+f18e, u+f190, u+f2d4
}

@font-face {
    font-family: "FontAwesome";
    font-display: block;
    src: url(../webfonts/pro-fa-regular-400-16.woff2) format("woff2"), url(../webfonts/pro-fa-regular-400-16.ttf) format("truetype");
    unicode-range: u+f27b
}

@font-face {
    font-family: "FontAwesome";
    font-display: block;
    src: url(../webfonts/pro-fa-v4compatibility.woff2) format("woff2"), url(../webfonts/pro-fa-v4compatibility.ttf) format("truetype");
    unicode-range: u+f041, u+f047, u+f065-f066, u+f07d-f07e, u+f080, u+f08b, u+f08e, u+f090, u+f09a, u+f0ac, u+f0ae, u+f0b2, u+f0d0, u+f0d6, u+f0e4, u+f0ec, u+f10a-f10b, u+f123, u+f13e, u+f148-f149, u+f14c, u+f156, u+f15e, u+f160-f161, u+f163, u+f175-f178, u+f195, u+f1f8, u+f219, u+f27a
}