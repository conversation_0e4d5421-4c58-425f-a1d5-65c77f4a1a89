@charset "utf-8";
.fak.fa-light-circle-dollar-lock,
.fa-kit.fa-light-circle-dollar-lock {
    --fa: "î€‚";
    --fa--fa: "î€‚î€‚"
}

.fak.fa-light-money-bill-1-wave-lock,
.fa-kit.fa-light-money-bill-1-wave-lock {
    --fa: "î€€";
    --fa--fa: "î€€î€€"
}

.fak.fa-light-money-bills-lock,
.fa-kit.fa-light-money-bills-lock {
    --fa: "î€";
    --fa--fa: "î€î€"
}

.fak.fa-sharp-solid-circle-dollar-slash,
.fa-kit.fa-sharp-solid-circle-dollar-slash {
    --fa: "î€„";
    --fa--fa: "î€„î€„"
}

.fakd.fa-sharp-duotone-solid-circle-dollar-circle-pause,
.fa-kit-duotone.fa-sharp-duotone-solid-circle-dollar-circle-pause {
    --fa: "î€ƒ";
    --fa--fa: "î€ƒî€ƒ"
}

.fak,
.fa-kit {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    display: var(--fa-display, inline-block);
    font-variant: normal;
    text-rendering: auto;
    font-family: Font Awesome Kit;
    font-style: normal;
    font-weight: 400;
    line-height: 1
}

.fak:before,
.fa-kit:before {
    content: var(--fa)
}

@font-face {
    font-family: Font Awesome Kit;
    font-style: normal;
    font-display: block;
    src: url(data:font/woff2;base64,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)format("woff2")
}

.fakd,
.fa-kit-duotone {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    display: var(--fa-display, inline-block);
    font-variant: normal;
    text-rendering: auto;
    letter-spacing: normal;
    font-family: Font Awesome Kit Duotone;
    font-style: normal;
    font-weight: 400;
    line-height: 1;
    position: relative
}

.fakd:before,
.fa-kit-duotone:before {
    content: var(--fa)
}

.fakd:after,
.fa-kit-duotone:after {
    content: var(--fa--fa)
}

.fa-kit-duotone:before,
.fakd:before {
    color: var(--fa-primary-color, inherit);
    opacity: var(--fa-primary-opacity, 1);
    position: absolute
}

.fa-kit-duotone:after,
.fakd:after {
    color: var(--fa-secondary-color, inherit);
    opacity: var(--fa-secondary-opacity, .4)
}

.fa-swap-opacity .fa-kit-duotone:before,
.fa-kit-duotone.fa-swap-opacity:before,
.fakd.fa-swap-opacity:before {
    opacity: var(--fa-secondary-opacity, .4)
}

.fa-swap-opacity .fa-kit-duotone:after,
.fa-kit-duotone.fa-swap-opacity:after,
.fakd.fa-swap-opacity:after {
    opacity: var(--fa-primary-opacity, 1)
}

.fa-kit-duotone.fa-inverse,
.fakd.fa-inverse {
    color: var(--fa-inverse, #fff)
}

.fa-kit-duotone.fa-stack-1x,
.fa-kit-duotone.fa-stack-2x,
.fakd.fa-stack-1x,
.fakd.fa-stack-2x {
    position: absolute
}

@font-face {
    font-family: Font Awesome Kit Duotone;
    font-style: normal;
    font-display: block;
    src: url(data:font/woff2;base64,d09GMgABAAAAAAUAAAsAAAAACEsAAAS1AAATAAAAAAAAAAAAAAAAAAAAAAAAAAAAATYCJAQgBmADDAA0yoRkywgFiE0HIBxYBSUEAMSe12nN+yLD2mtW7KVvOSsl7G9S7PXe8SKWdkBJHLBylg4I6qtYRXVUumgOinKBygOq4j/uVaQnnqU21stYtuY+T+xfKBSBMXZLJQl2gdQKJsW6AVv/oO2dAwKANIKX3uOdve1/EGVvACDcful9lg0bDdlrNAQQQieDCaDhaAiw14CEzpVdQIk9RMJTUIBqA6HL1TKbos+ggNEZWgVwvHrSP8jjLYBBkShbKIHCkEevXz6L4Q3phqSHiYZ4RMoxoCZ9C4BDpdcA9hp1taTr7pIAZLTrl4EMDkCGPByMN6TRqEp+aHe4/FVQgNGQXqMhVMSQwT7UAdvWbEsk9WqpagnN1i09KTS7VC3pwi42bCEadlHk9JyeFKqmakVhWqaVLHue5x24d++e533meZ537d69e563+zPF4hnimXiMKBbP7N7IxGNUCIIg2P1uKgh+C4KAzgS/BUEmHiOKxTO73Uz3B9IXkKg7GrLXcAMFTKMNKBUdHmvUa5ZpmSVDUzU1m9Fzeq5qZevZrCiJerFjVWSrDZ+i361iMZNOZ4pFq+59Cc4f4fyRdNowTNMw0mkj/acLFy4kEhcSCY85bSwaDel/GmIHSKtZIXQYplds06rZqqZqmhxuWqZlWoZlWLbIVXPVnJ7Tc3bDbrQbUCuZlmk37Iat3gFCiJyuCdMyNVVT9ZwuhnJow27YlWqZ73+gaqpmGMZ4q3XsSHWGsXB0T3w6k2QUCjFGLMaIUTSajRdiESaFEylZojH9kBI+EI5M7y9E8mP5vSFJCsX2FfIpRZ+crMlyODneah071mqN0yNHH3/8aNsoUl4c2xMNyQodthSFiElEk0SypEmSKofCakQiIiWWGS9GZWYeVrQpTZY0WdXUkKbGY9mZfIGKh9pGkahotAFcwOdUAZCjOGiSVz2Jo/VAGMNvGgatO5BwEG+QyqCgjC9ARQ4PVBGY82EoEIUOs505gQSmeUzKGCWB5DCAD+jTeiCU2ffAEJcESGizfLXcHSjosqugYobdhQjOUBaimKNFSIBLSXcScUm8OLGb95bd/rfi/qbTPZ/77qbT50uVFafZXFpaWFr3/R2vNT+/6vb9xWcdz9125pbd7RubnvX49uLzfN3ZWuHfqZv2zOKg5z7t8fcqEN2Bx79TmCEs+085s//pM6nvH3P7Pn+UmK7Az19y1p7eWhzMlufKc81rzsDruX1enivbtYpYsOt8AsV+laasxYOmJhlNx9y+34p4uufPOjkIpvhXYhlPw4MPF9vg6GEZLvrwwLGKgRV4Gj344F2+7SYc9MGxhApW4KCJJpawhAUsYR0+fOzAQwvzmMcqX6KPRTwLB56vcDAnZ9E2HoeLHTyPAXpYwx/igWMbi3geHOtwsIUVafi8/MR1OOB4BosodHVP5FjGAA4W+wIHTVtdrGJy+gp72rt8rKPXlwwdBceq2zke1Tar7xMvwcEansaWBo5ZlDFXdjRxDY5s7PEJeNvva6OGCgQWYKMOjgk5tc+wiFp+b+fQxKR+LEwej+HRMlsUfx4va9VZRCXSQmH9QcyAthb9PkKQAWBXIGKrt7aI0GpbIUNbhWOoHlEtSjXrx8BAIBDIAAMAAAA)format("woff2")
}